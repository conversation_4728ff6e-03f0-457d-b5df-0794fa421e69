<?php include_once '../lib/config.php';
user();

// Function to validate BEP20 address format
function validateBEP20Address($address) {
    // BEP20 addresses should be 42 characters long and start with 0x
    if (strlen($address) !== 42 || substr($address, 0, 2) !== '0x') {
        return false;
    }

    // Check if the address contains only hexadecimal characters after 0x
    $hex_part = substr($address, 2);
    return ctype_xdigit($hex_part);
}

// Function to check if user has previously set a BEP20 address (requires OTP for updates)
function hasSetBEP20Address($uid) {
    // Check if user already has a bitcoin address in user table
    $query = "SELECT bitcoin FROM user WHERE uid = '$uid' AND bitcoin != '' AND bitcoin IS NOT NULL";
    $result = my_query($query);

    return mysqli_num_rows($result) > 0;
}

$arr = array(
    'bitcoin' => '',
    'bnb_address' => '',
);

if(isset($_POST)){
    $uid = tres($_POST['uid']);
    $user = get_user_details($uid);
    
    $name = isset($_POST['name']) ? tres($_POST['name']) : '';
    $dob = isset($_POST['dob']) ? date('Y-m-d', strtotime(tres($_POST['dob']))) : date('Y-m-d');
    $gender = isset($_POST['gender']) ? tres($_POST['gender']) : 'Male';
    $email = isset($_POST['email']) ? tres($_POST['email']) : '';
    $phone = isset($_POST['phone']) ? tres($_POST['phone']) : '';
    $mobile = isset($_POST['mobile']) ? tres($_POST['mobile']) : '';
    $address = isset($_POST['address']) ? tres($_POST['address']) : '';
    $city = isset($_POST['city']) ? tres($_POST['city']) : '';
    $state = isset($_POST['state']) ? tres($_POST['state']) : '';
    $country = isset($_POST['country']) ? tres($_POST['country']) : 'IN';

    foreach ($arr as $key => $value) {
        ${$key} = isset($_POST[$key]) ? tres($_POST[$key]) : $value;
    }
    
    if(checkMobile($mobile)==0){
        setMessage('Invalid mobile.','error');
    }
    elseif(checkMobileAvailability($mobile, $uid)==0){
        setMessage('Mobile already axist.','error');
    }
    elseif(checkEmail($email)==0){
        setMessage('Invalid email.','error');
    }
    elseif(checkEmailAvailability($email, $uid)==0){
        setMessage('Email already axist.','error');
    }
    // BEP20 Address validation
    elseif(isset($_POST['bitcoin']) && !empty($_POST['bitcoin']) && !validateBEP20Address($_POST['bitcoin'])){
        setMessage('Invalid BEP20 address format. Address must be 42 characters long and start with 0x.','error');
    }
    // Check if user is updating BEP20 address for second time and needs OTP verification
    elseif(isset($_POST['bitcoin']) && !empty($_POST['bitcoin']) && hasSetBEP20Address($uid) && (!isset($_POST['otp_verified']) || $_POST['otp_verified'] !== 'true')){
        setMessage('OTP verification required for BEP20 address update. Please verify your email first.','error');
    }
    else{
        $sql = "UPDATE user SET name='".$name."', dob='".$dob."', gender='".$gender."', email='".$email."', phone='".$phone."'";
        $sql .= ", mobile='".$mobile."', address='".$address."', city='".$city."', state='".$state."', country='".$country."'";
        
        foreach ($arr as $key => $value) {
            if (isset($_POST[$key])) {
                $sql .= ", $key = '" . ${$key} . "'";
            }
        }
        
        $sql .= " WHERE uid='".$uid."'";
        my_query( $sql);

        setMessage('Profile edit successfully.', 'success');
    }
    redirect('./profile.php');
}
else{
    redirect('./profile.php');
}
?>
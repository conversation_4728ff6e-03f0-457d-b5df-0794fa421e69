<?php
// Simple debug - write to a file to see if this script is called
file_put_contents('debug.txt', 'update_wallet_model.php called at ' . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
file_put_contents('debug.txt', 'POST: ' . print_r($_POST, true) . "\n", FILE_APPEND);

session_start();
include_once '../lib/config.php';
user();

// Function to validate BEP20 address format
function validateBEP20Address($address) {
    // BEP20 addresses should be 42 characters long and start with 0x
    if (strlen($address) !== 42 || substr($address, 0, 2) !== '0x') {
        return false;
    }

    // Check if the address contains only hexadecimal characters after 0x
    $hex_part = substr($address, 2);
    return ctype_xdigit($hex_part);
}

if(isset($_POST['uid'])){
    $uid = tres($_POST['uid']);
    $fields = ['bitcoin', 'bnb_address'];

    // Simple validation for BEP20 address
    if (isset($_POST['bitcoin']) && !empty($_POST['bitcoin'])) {
        $newBep20 = tres($_POST['bitcoin']);

        // Validate BEP20 address format
        if (!validateBEP20Address($newBep20)) {
            setMessage('Invalid BEP20 address format. Address must be 42 characters long and start with 0x.', 'error');
            redirect('./profile.php');
            exit;
        }
    }

    $updates = [];
    foreach ($fields as $key) {
        if (isset($_POST[$key])) {
            $val = tres($_POST[$key]);
            $updates[] = "`$key` = '$val'";
        }
    }

    if (!empty($updates)) {
        $sql = "UPDATE user SET " . implode(', ', $updates) . " WHERE uid = '$uid'";
        my_query($sql);
        setMessage("Wallet info updated successfully.", "success");
    } else {
        setMessage("No changes made.", "warning");
    }
} else {
    setMessage("Invalid request.", "error");
}

redirect('./profile.php');

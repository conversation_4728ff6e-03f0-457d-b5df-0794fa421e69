<?php
$title = "Profile";
ob_start();
session_start();
include_once '../lib/config.php';
// include_once 'header.php';
ini_set('display_errors', 1);
error_reporting(E_ALL);
$uid = $_SESSION['userid'];
$query = "SELECT u.*, r.login_id as sponsor_login_id, r.name as sponsor_name FROM user as u"
        . " LEFT JOIN user as r ON r.uid=u.refer_id WHERE u.uid='".$uid."'";
$row = mysqli_fetch_object(my_query( $query));
$user = get_user_details($uid);
$reward_arr = get_reward();
$locarr = array(
    'city' => array(
        'a' => 1,
        'ml' => 50,
        'rq' => 0,
    ),
    'state' => array(
        'a' => 1,
        'ml' => 50,
        'rq' => 0,
    )
);
$acarr = array(
    'ifsc' => array(
        'a' => 1,
        'ml' => 20,
        'rq' => 0,
    ),
    'bank_name' => array(
        'a' => 1,
        'ml' => 100,
        'rq' => 0,
    ),
    'branch_name' => array(
        'a' => 1,
        'ml' => 100,
        'rq' => 0,
    )
);
$otherarr = array(
    'bitcoin' => array(
        'a' => 1,
        'ml' => 100,
        'rq' => 1,
        'nm' => 'USDT.BEP20 Address',
    )
);

?>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title>Dashboard </title><meta name="viewport"
		content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.10.1/css/all.min.css" integrity="sha512-9my9Mb2+0YO+I4PUCSwUYO7sEK21Y0STBAiFEYoWtd2VzLEZZ4QARDrZ30hdM1GlioHJ8o8cWQiy8IAb1hy/Hg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
	width: 150px;
}
.db-page--wide-menu .db-side-logo__text-block {
  width: 200px !important;
}.db-side-logo__text {
  padding-left: 0 !important;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 150px;
}
.db-side-logo__text .image {
  width: 150px;
  min-width: 61px;
  max-width: none;
}
.db-page-topline-panel__right__logo .logo-wrapper {
  max-width: 160px;
}
.topline-refill-btn {
  line-height: 1;
  color: #EBF4FF;
  font-size: 15px;
  text-align: center;
  height: 40px;
}
.topline-refill-btn {
  width: 170px;
}
.toplinen img {
  width: 17px;
}
@media only screen and (min-width: 601px) {
  .db-page-topline__right {
  margin-left: -200px;
}
}

.dropdown-container {
  margin-top: 5px;
  border-radius: 5px;
  padding-bottom: 5px;
}
.dropdown-container {
  display: none;
  /*background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);**/
  border-radius:5px;}
/* Optional: Style the caret down icon */


/* Some media queries for responsiveness */
@media screen and (max-width: 780px) {
 .mobhidden{display:none !important;}
  .cabMenu li.hidden {
    display: flex !important;
  }
  .langList{display:none}
}

.dropdown-btn .fa {
  position: absolute;
  right: 5px;
  top: 65px;
  font-size: 20px;
  color: #fff;
}
.fa-caret-up{display:none;}
.dactive .fa-caret-down{display:none;}
.dactive .fa-caret-up{display:inline-block !important;}
.dropdown-container li {
  height: auto;
  padding: 5px 10px 5px 10px;
  line-height: 1;

}
.dropdown-containers li:hover{
 background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);;
 color:#fff;
}
.dropdown-container li:hover cap{
 color:#fff;
}
.hidden:hover {
  background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.capa a {
	margin-left:20px;  color: #fff;
  text-decoration: none;
  font-size:14px;
}
.db-sidemenu-icon img {
  width: 25px;
}
.db-sidemenu-icon {
  position: inherit;
}
.mobile-db-menu-link__text {
  margin-left: 5px;
}
.field--nb .field-icon::before{background:transparent;}
.field--nb  img {
  margin-top: -10px;
  height: 17px;
}
</style>
<link rel="shortcut icon" href="./assets/fav.png" />
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
</head>

<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>

<div class="mobile-panel-block">
  <div class="mobile-panel-top-block">
    <div class="mobile-panel-top-left">
      <div class="logo-wrapper"> <a href="" class="logo">
        <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
        </a> </div>
    </div>
    <div class="mobile-panel-top-right">
      <div class="topline-lang-panel-block">
        <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
								aria-expanded="false">
          <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
          <div class="current-lang__text">EN</div>
          </a>
          <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
            <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
            <div class="lang-link__text">EN</div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-panel-close-btn-block">
        <button type="button" class="mobile-panel-close-btn" onclick="closeSidebar()"></button>
      </div>
    </div>
  </div>
  <div class="mobile-db-menu-block">
    <div class="mobile-db-menu">
      <ul>
  <li class="mobile-db-menu-item mobile-db-menu-item--dashboard"> <a href='dashboard.php' class="mobile-db-menu-link mobile-db-menu-link--dashboard">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Dashboard</div>
    </div>
    </a> </li>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings active">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--refill ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--withdrawal ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
    <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
     <li class="hidden" ><span class="capa"><a href="report_growth.php" class="openMod" data-modal="buy">Non-Working Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_royalty.php?type=1">Lifetime Cash Back Income</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_binary.php" >Matching Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
    <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--affiliate ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <!--<li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>-->
    <!--<li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>-->
    <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
    <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/lotery.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Lotteries <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="#" class="openMod" data-modal="buy">Buy Lottery</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="#">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/gaming.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Gaming <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="#" class="openMod" data-modal="buy">Play Games</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="#">Report</a></span> </li>
  </div>
  <!--<li class="mobile-db-menu-item mobile-db-menu-item--settings"> <a href='#' class="mobile-db-menu-link mobile-db-menu-link--settings">-->
  <!--  <div class="db-sidemenu-icon"><img src="assets/promo.png"></div>-->
  <!--  <div class="mobile-db-menu-link__text-block">-->
  <!--    <div class="mobile-db-menu-link__text">Promo Stuff</div>-->
  <!--  </div>-->
  <!--  </a> </li>-->
  <!--  <li class="mobile-db-menu-item mobile-db-menu-item--settings"> <a href='#' class="mobile-db-menu-link mobile-db-menu-link--settings">-->
  <!--  <div class="db-sidemenu-icon"><img src="assets/support.png"></div>-->
  <!--  <div class="mobile-db-menu-link__text-block">-->
  <!--    <div class="mobile-db-menu-link__text">Support Ticket</div>-->
  <!--  </div>-->
  <!--  </a> </li>-->
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="logout.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="topline-logout-btn__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Logout</div>
    </div>
    </a> </li>
</ul>
    </div>
  </div>
  <div class="mobile-panel-menu-block">
    <div class="mobile-panel-menu">
      <div class="mobile-menu">

      </div>
    </div>
  </div>
</div>
<div class="db-page-block">
<div class="container">
<div class="row">
<div class="col-12">
<div class="db-page db-page--wide-menu">
<div class="db-page-left">
  <div class="db-side-block">
    <div class="db-side-logo-block"> <a href="" class="db-side-logo">
      <div class="db-side-logo__text-block">
        <div class="db-side-logo__text"> <img class="image" src="assets/logo.png" alt=""> </div>
      </div>
      </a> </div>
    <div class="db-side-toggle-panel-btn-block">
      <button class="db-side-toggle-panel-btn">
      <div class="db-side-toggle-panel-btn__text-block">
        <div class="db-side-toggle-panel-btn__text">Navigation</div>
      </div>
      <div class="db-side-toggle-panel-btn__icon"></div>
      </button>
    </div>
    <div class="db-sidemenu-block">
      <div class="db-sidemenu">
        <ul>
          <li class="db-sidemenu-item db-sidemenu-item--dashboard"> <a href='dashboard.php' class="db-sidemenu-link db-sidemenu-link--dashboard">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Dashboard</div>
            </div>
            </a> </li>
          <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings active">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
             <div class="db-sidemenu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--refill ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--withdrawal ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
      </div>


          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
             <li class="hidden" ><span class="capa"><a href="report_growth.php" class="openMod" data-modal="buy">Non-Working Bonus</a></span> </li>
            <li class="hidden" ><span class="capa"><a href="report_royalty.php?type=1">Lifetime Cash Back Income</a></span> </li>
            <li class="hidden" ><span class="capa"><a href="report_binary.php" >Matching Bonus</a></span> </li>
            <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
             <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->

      </div>
          <li class="db-sidemenu-item db-sidemenu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--affiliate ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <!--<li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>-->
             <!--<li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>-->
             <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
             <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
         </div>


             <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
      </div>
      <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/lotery.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Lotteries  <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="#" class="openMod" data-modal="buy">Buy Lottery</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="#">Report</a></span> </li>
      </div>
      <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/gaming.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Gaming   <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="#" class="openMod" data-modal="buy">Play Games</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="#">Report</a></span> </li>
      </div>
      <!--<li class="db-sidemenu-item db-sidemenu-item--settings"> <a href='#' class="db-sidemenu-link db-sidemenu-link--settings">-->
      <!--      <div class="db-sidemenu-icon"><img src="assets/promo.png"></div>-->
      <!--      <div class="db-sidemenu-link__text-block">-->
      <!--        <div class="db-sidemenu-link__text">Promo Stuff </div>-->
      <!--      </div>-->
      <!--      </a> </li>-->
      <!--      <li class="db-sidemenu-item db-sidemenu-item--settings"> <a href='#' class="db-sidemenu-link db-sidemenu-link--settings">-->
      <!--      <div class="db-sidemenu-icon"><img src="assets/support.png"></div>-->
      <!--      <div class="db-sidemenu-link__text-block">-->
      <!--        <div class="db-sidemenu-link__text">Support Ticket </div>-->
      <!--      </div>-->
      <!--      </a> </li>-->

            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="logout.php" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="topline-logout-btn__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Logout</div>
            </div>
            </a> </li>






        </ul>
      </div>
    </div>
  </div>
</div>
<div class="db-page-right">
<div class="db-page-topline-block">
  <div class="db-page-topline">
    <div class="db-page-topline__left">
      <div class="topmenu-block">
        <div class="topmenu priority-nav priority-nav-has-dropdown 224">

        </div>
      </div>
    </div>
    <div class="db-page-topline__right">
      <div class="db-page-topline-panel-block">
        <div class="db-page-topline-panel">
          <div class="db-page-topline-panel__left">

            <div class="topline-refill-btn-block"> <a href="profile.php" class="topline-refill-btn">
             <div class="toplinen"><img src="assets/ranking.png"></div>
              <div class="topline-refill-btn__text">Rank : <?php echo $reward_arr[$user->reward] ?? 'Starter'; ?></div>
              </a> </div>

            <div class="topline-refill-btn-block"> <a href="deposit_block.php" class="topline-refill-btn">
              <div class="topline-refill-btn__icon"></div>
              <div class="topline-refill-btn__text">Deposit</div>
              </a> </div>
          </div>
          <div class="db-page-topline-panel__right">
            <div class="db-page-topline-panel__right__logo">
              <div class="logo-wrapper"> <a href="" class="logo">
                <div class="logo-img"> <img src="assets/logo.png"
																				class="image" alt=""> </div>
                </a> </div>
            </div>
            <div class="db-page-topline-panel__right__content">
              <div class="topline-lang-panel-block">
                <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
								aria-expanded="false">
                  <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                  <div class="current-lang__text">EN</div>
                  </a>
                  <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                    <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                    <div class="lang-link__text">EN</div>
                    </a> </div>
                </div>
              </div>
              <div class="topline-logout-btn-block"> <a href="logout.php" class="topline-logout-btn">
                <div class="topline-logout-btn__icon"></div>
                </a> </div>
              <div class="mobile-panel-btn-block">
                <button type="button" class="mobile-panel-btn" onclick="show()"></button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
    function show(){
       document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block active")
    }
    function closeSidebar(){
        document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block")
    }
</script>
<script>
/* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content - This allows the user to have multiple dropdowns without any conflict */
var dropdown = document.getElementsByClassName("dropdown-btn");
var i;

for (i = 0; i < dropdown.length; i++) {
  dropdown[
i].addEventListener("click", function() {
    this.classList.toggle("active");
	this.classList.toggle("dactive");
    var dropdownContent = this.nextElementSibling;
    if (dropdownContent.style.display === "block") {
      dropdownContent.style.display = "none";


    } else {
      dropdownContent.style.display = "block";
    }
  });
}
</script><style>
.field--nb .field-icon::before{background:transparent;}
.field--nb  img {
  margin-top: -10px;
  height: 17px;
}

.bep20-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.bep20-buttons .purple-btn, .bep20-buttons .green-btn {
  padding: 8px 16px;
  font-size: 14px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.bep20-buttons .purple-btn {
  background-color: #6f42c1;
  color: white;
}

.bep20-buttons .purple-btn:hover {
  background-color: #5a359a;
}

.bep20-buttons .green-btn {
  background-color: #28a745;
  color: white;
}

.bep20-buttons .green-btn:hover {
  background-color: #218838;
}

#saveBtn:disabled {
  background-color: #6c757d !important;
  cursor: not-allowed !important;
  opacity: 0.6;
  pointer-events: none;
}

#saveBtn:disabled:hover {
  background-color: #6c757d !important;
}

#saveBtn:not(:disabled) {
  background-color: #6f42c1;
  cursor: pointer;
}
</style>
<div class="db-page-content-block">
  <div class="db-page-content">
    <?php echo getMessage() ?>
    <div class="setting-block">
      <div class="setting">
        <div class="setting-left">
          <div class="setting-side-block">
            <div class="setting-side">
              <h1> Personal Information </h1>
              <div class="setting-side-tabs-content-block">
                <div class="setting-side-tabs-content">
                  <div class="setting-side-tab-content">
                    <div class="setting-common-form-block">
                      <form action="profile_model.php" class="setting-common-form form" method="post">


                        <div class="field-block">
                          <div class="field-title-block">
                            <div class="field-title"> User ID <span class="field-required-star">*</span></div>
                          </div>
                          <div class="field field--input field--have-icon field--nb">
                            <div class="field-icon"><img src="assets/userid.png"></div>
                            <input type="text" name="login_id" autocomplete="off" value="<?php echo $row->login_id?>" <?php if(!empty($row->login_id)){ echo "readonly";}?> maxlength="100" required pattern="\w{6,100}">
                            <div class="field-right-panel-block">
                              <div class="field-right-panel">
                                <div class="field-status-block">
                                  <div class="field-status field-status--success"> </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="field-block">
                          <div class="field-title-block">
                            <div class="field-title"> Name <span class="field-required-star">*</span></div>
                          </div>
                          <div class="field field--input field--have-icon field--username">
                            <div class="field-icon"></div>
                            <input type="text" name="name" autocomplete="off" value="<?php echo $row->name?>" <?php if(!empty($row->name)){ echo "readonly";}?> maxlength="50" required pattern="[a-zA-Z ]+">
                            <div class="field-right-panel-block">
                              <div class="field-right-panel">
                                <div class="field-status-block">
                                  <div class="field-status field-status--success"> </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="field-message-block">
                            <div class="field-message">

                            </div>
                          </div>
                        </div>
                        <div class="field-block">
                          <div class="field-title-block">
                            <div class="field-title"> Email <span class="field-required-star">*</span></div>
                          </div>
                          <div class="field field--input field--have-icon field--email">
                            <div class="field-icon"></div>
                            <input type="email" name="email" autocomplete="off" value="<?php echo $row->email?>" <?php if(!empty($row->email)){ echo "readonly";}?> maxlength="50" required>
                            <div class="field-right-panel-block">
                              <div class="field-right-panel">
                                <div class="field-status-block">
                                  <div class="field-status field-status--success"> </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="field-block">
                          <div class="field-title-block">
                            <div class="field-title"> Contact <span class="field-required-star">*</span></div>
                          </div>
                          <div class="field field--input field--have-icon field--nb">
                            <div class="field-icon"><img src="assets/phone.png"></div>
                            <input type="text" name="mobile" autocomplete="off" value="<?php echo $row->mobile?>" <?php if(!empty($row->mobile)){ echo "readonly";}?> maxlength="10" required pattern="[0-9]{10,10}">
                            <div class="field-right-panel-block">
                              <div class="field-right-panel">
                                <div class="field-status-block">
                                  <div class="field-status field-status--success"> </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="field-message-block">
                            <div class="field-message">

                            </div>
                          </div>
                        </div>
                        <div class="field-block">
                          <div class="field-title-block">
                            <div class="field-title"> Country <span class="field-required-star">*</span></div>
                          </div>
                          <div class="field field--input field--have-icon field--nb">
                            <div class="field-icon"><img src="assets/country.png"></div>
                            <?php
                            // Get country name for display
                            $country_name = '';
                            if(!empty($row->country)) {
                                $country_result = my_query("SELECT short_name FROM country WHERE country_id = '".$row->country."'");
                                if($country_row = my_fetch_object($country_result)) {
                                    $country_name = $country_row->short_name;
                                }
                            }
                            ?>
                            <input type="text" name="country_display" autocomplete="off" value="<?php echo $country_name; ?>" <?php if(!empty($row->country)){ echo "readonly";}?> placeholder="Select Country">
                            <input type="hidden" name="country" value="<?php echo $row->country; ?>">
                            <div class="field-right-panel-block">
                              <div class="field-right-panel">
                                <div class="field-status-block">
                                  <div class="field-status field-status--success"> </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <?php foreach ($locarr as $key => $value) {
                            if($value['a']){
                        ?>
                        <div class="field-block">
                          <div class="field-title-block">
                            <div class="field-title"> <?php echo ucwords(str_replace('_', ' ', $key));?> <?php echo isset($value['rq']) && $value['rq'] ? '<span class="field-required-star">*</span>' : '';?></div>
                          </div>
                          <div class="field field--input field--have-icon field--nb">
                            <div class="field-icon"><img src="assets/<?php echo $key;?>.png"></div>
                            <input type="text" name="<?php echo $key;?>" autocomplete="off" value="<?php echo $row->$key?>" <?php if(!empty($row->$key)){ echo "readonly";}?> maxlength="<?php echo isset($value['ml']) && $value['ml'] ? $value['ml'] : 100;?>" <?php echo isset($value['rq']) && $value['rq'] ? 'required' : '';?>>
                            <div class="field-right-panel-block">
                              <div class="field-right-panel">
                                <div class="field-status-block">
                                  <div class="field-status field-status--success"> </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="field-message-block">
                            <div class="field-message">

                            </div>
                          </div>
                        </div>
                        <?php }}?>

                        <div class="field-block">
                          <div class="field-title-block">
                            <div class="field-title"> Gender </div>
                          </div>
                          <div class="field field--input field--have-icon field--nb">
                            <div class="field-icon"><img src="assets/username.png"></div>
                            <input type="text" name="gender" autocomplete="off" value="<?php echo $row->gender; ?>" <?php if(!empty($row->gender)){ echo "readonly";}?> placeholder="Select Gender">
                            <div class="field-right-panel-block">
                              <div class="field-right-panel">
                                <div class="field-status-block">
                                  <div class="field-status field-status--success"> </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="field-message-block">
                            <div class="field-message">

                            </div>
                          </div>
                        </div>
                        <div class="field-block">
                          <div class="field-title-block">
                            <div class="field-title"> Date of Joining </div>
                          </div>
                          <div class="field field--input field--have-icon field--nb">
                            <div class="field-icon"><img src="assets/date.png"></div>
                            <input type="text" name="datetime" autocomplete="off" value="<?php echo date("d M, Y h:i A", strtotime($row->datetime));?>" readonly>
                            <div class="field-right-panel-block">
                              <div class="field-right-panel">
                                <div class="field-status-block">
                                  <div class="field-status field-status--success"> </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="field-message-block">
                            <div class="field-message">

                            </div>
                          </div>
                        </div>

                        <div class="form-button-block">
                          <input type="hidden" name="uid" value="<?php echo $row->uid?>" />
                          <input type="hidden" name="otp_verified" id="otp_verified" value="false" />
                          <button type="submit" id="saveWalletBtn" class="purple-btn send-btn"> Save </button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="setting-block">
      <div class="setting">
        <div class="setting-left">
          <div class="setting-side-block">
            <div class="setting-side">
              <h1> Sponsor Information </h1>
              <div class="setting-side-tabs-content-block">
                <div class="setting-side-tabs-content">
                  <div class="setting-side-tab-content">
                    <div class="setting-common-form-block">
                      <form class="setting-common-form form">


                        <div class="field-block">
                          <div class="field-title-block">
                            <div class="field-title"> User ID </div>
                          </div>
                          <div class="field field--input field--have-icon field--nb">
                            <div class="field-icon"><img src="assets/userid.png"></div>
                            <input type="text" name="sponsor_login_id" autocomplete="off" value="<?php echo $row->sponsor_login_id?>" readonly maxlength="20">
                            <div class="field-right-panel-block">
                              <div class="field-right-panel">
                                <div class="field-status-block">
                                  <div class="field-status field-status--success"> </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="field-block">
                          <div class="field-title-block">
                            <div class="field-title"> Name </div>
                          </div>
                          <div class="field field--input field--have-icon field--username">
                            <div class="field-icon"></div>
                            <input type="text" name="sponsor_name" autocomplete="off" value="<?php echo $row->sponsor_name?>" readonly maxlength="50">
                            <div class="field-right-panel-block">
                              <div class="field-right-panel">
                                <div class="field-status-block">
                                  <div class="field-status field-status--success"> </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="field-message-block">
                            <div class="field-message">

                            </div>
                          </div>
                        </div>






                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


        <div class="setting-block">
      <div class="setting">
        <div class="setting-left">
          <div class="setting-side-block">
            <div class="setting-side">
              <h1> Payment Information
 </h1>
              <div class="setting-side-tabs-content-block">
                <div class="setting-side-tabs-content">
                  <div class="setting-side-tab-content">
                    <div class="setting-common-form-block">
                      <form action="update_wallet_model.php" class="setting-common-form wallet-form form" method="post">

                        <?php foreach ($otherarr as $key => $value) {
                            if($value['a']){
                        ?>
                        <div class="field-block">
                          <div class="field-title-block">
                            <div class="field-title"> <?php echo ucwords(str_replace('_', ' ', (isset($value['nm']) && $value['nm']) ? $value['nm'] : $key));?> <?php echo isset($value['rq']) && $value['rq'] ? '<span class="field-required-star">*</span>' : '';?> </div>
                          </div>
                          <div class="field field--input field--have-icon field--wallet">
                            <div class="field-icon"></div>
                            <input type="text" id="<?php echo $key;?>_input" name="<?php echo $key;?>" autocomplete="off" value="<?php echo $row->$key?>" data-original="<?php echo $row->$key?>" <?php if(!empty($row->$key)){ echo "readonly";}?> maxlength="<?php echo isset($value['ml']) && $value['ml'] ? $value['ml'] : 100;?>" <?php echo isset($value['rq']) && $value['rq'] ? 'required' : '';?>>
                            <div class="field-right-panel-block">
                              <div class="field-right-panel">
                                <div class="field-status-block">
                                  <div class="field-status field-status--success"> </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <?php if($key == 'bitcoin' && !empty($row->$key)): ?>
                          <div class="bep20-buttons" style="margin-top: 10px;">
                            <button type="button" id="editBep20Btn" class="purple-btn" style="margin-right: 10px;">Edit Address</button>
                            <button type="button" id="sendOtpBtn" class="green-btn" style="display: none;">Send OTP</button>
                          </div>
                          <?php endif; ?>
                        </div>
                        <?php }}?>







                        <div class="form-button-block">
                          <input type="hidden" name="uid" value="<?php echo $row->uid?>" />
                          <input type="hidden" name="otp_verified" id="otp_verified" value="false" />
                          <button type="submit" id="saveBtn" class="purple-btn send-btn" <?php echo !empty($row->bitcoin) ? "disabled" : "" ?> > Save </button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>


</div>
<script>
		function depositDetails(button) {
			modal = $('#modalDeposit');
			if (button.data('status') == 'completed') {
				modal.find('.modalDepositActive').addClass('d-none');
				modal.find('.modalDepositCompleted').removeClass('d-none');
			} else {
				modal.find('.modalDepositActive').removeClass('d-none');
				modal.find('.modalDepositCompleted').addClass('d-none');
			}
			modal.find('.modalDepositPercent').html(button.data('plan'));
			modal.find('.modalDepositProgress').attr('style','width:'+button.data('progress')+'%');
			currencyInfo = globalCurrencies[button.data('currency')];
			modal.find('.modalDepositCurrencyIcon').html(currencyInfo.title);
			modal.find('.modalDepositCurrencyIcon').attr('src','assets/cy/images/svg/payment/'+currencyInfo.config.data.icon+'.svg');
			modal.find('.modalDepositDailyReturn').html(button.data('percent-amount'));
			modal.find('.modalDepositAmount').html(button.data('amount'));
			modal.find('.modalDepositTotalReturn').html(button.data('total-return'));
			modal.find('.modalDepositDuration').html(button.data('duration'));
			modal.find('.modalDepositStart').html(button.data('start'));
			modal.find('.modalDepositEnd').html(button.data('end'));

			modal.modal('show');
		}
	</script>
<div class="modals">
  <div class="modal custom-modal fade" id="modalConfirm" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title-block">
            <div class="modal-title h2" >Security Verification</div>
          </div>
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <div class="add-wallet-form-block">
              <form action="/check_confirm/" class="form add-wallet-form">
                <div class="field-block">
                  <div class="field-title-block">
                    <div class="field-title"> Google 2FA Code </div>
                  </div>
                  <div
													class="field field--input field--have-icon field--pin">
                    <div class="field-icon"></div>
                    <input type="text" name="secret" maxlength="7" class="form-control" value="">
                  </div>
                </div>
                <div class="form-button-block">
                  <button type="submit" class="green-gr-btn send-btn">
                  <div class="send-btn__text">Confirm</div>
                  <div class="send-btn__icon"></div>
                  </button>
                </div>
                <input type="hidden" name="code" value="">
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- BEP20 OTP Verification Modal -->
  <div class="modal custom-modal fade" id="modalBep20Otp" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title-block">
            <div class="modal-title h2">BEP20 Address Verification</div>
          </div>
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <p>For security reasons, updating your USDT BEP20 address requires verification.</p>
            <div class="otp-form-block">
              <div class="field-block">
                <div class="field-title-block">
                  <div class="field-title">Enter OTP Code</div>
                </div>
                <div class="field field--input field--have-icon field--pin">
                  <div class="field-icon"></div>
                  <input type="text" id="bep20_otp" name="bep20_otp" maxlength="6" class="form-control" placeholder="Enter 6-digit OTP">
                </div>
                <div id="otpMessage" style="margin-top: 10px; color: #ff0000;"></div>
              </div>
              <div class="form-button-block" style="display: flex; justify-content: space-between; margin-top: 20px;">
                <button type="button" id="resendOtpBtn" class="purple-btn">Resend OTP</button>
                <button type="button" id="verifyOtpBtn" class="green-gr-btn" >Verify & Update</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Success Modal -->
  <div class="modal custom-modal fade" id="modalSuccess" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title-block">
            <div class="modal-title h2">Success</div>
          </div>
          <button type="button" class="modal-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="modal-body-content">
            <div class="text-center">
              <div class="mb-3">
                <i class="fas fa-check-circle" style="font-size: 48px; color: #28a745;"></i>
              </div>
              <p>Profile updated successfully!</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="./assets/cy/libs/jquery/jquery.min.js?vs=100"></script>
<script src="./assets/cy/libs/bootstrap/js/bootstrap.bundle.min.js?vs=100"></script>
<script src="./assets/cy/libs/priority-navigation-master/dist/priority-nav.min.js?vs=100"></script>
<script src="./assets/cy/libs/swiper/swiper.min.js?vs=100"></script>
<script src="./assets/cy/libs/bootstrap-select-1.14.0/dist/js/bootstrap-select.min.js?vs=100"></script>
<script src="./assets/cy/libs/plyr/dist/plyr.min.js?vs=100"></script>
<script src="./assets/cy/libs/ion.rangeSlider-master/js/ion.rangeSlider.min.js?vs=100"></script>
<script src="./assets/cy/libs/toastr-master/build/toastr.min.js?vs=100"></script>
<script src="./assets/cy/js/main.js?vs=100"></script>

<script>
// Show success modal if there's a success parameter in URL
$(document).ready(function() {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('success') === '1') {
        $('#modalSuccess').modal('show');
        // Remove the success parameter from URL without refreshing
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
    }

    // Show error message if there's an error parameter
    if (urlParams.get('error') === '1') {
        const message = urlParams.get('message') || 'An error occurred while updating profile.';
        alert('Error: ' + decodeURIComponent(message));
        // Remove the error parameter from URL without refreshing
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
    }



    // Handle Personal Information form submission
    $('.setting-common-form').on('submit', function(e) {
        e.preventDefault();

        const form = $(this);
        const formData = new FormData(form[0]);

        // Handle Personal Information form
        if (form.attr('action') === 'profile_model.php') {
            $.ajax({
                url: form.attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    // Show success modal
                    $('#modalSuccess').modal('show');

                    // Optionally reload the page after a delay
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                },
                error: function(xhr, status, error) {
                    // Handle error - you can add error modal here
                    alert('Error updating profile. Please try again.');
                }
            });
        }
    });

    // Force form submission when save button is clicked
    $('#saveBtn').on('click', function(e) {
        e.preventDefault(); // Prevent default form submission

        console.log('Save button clicked - forcing form submission');
        console.log('OTP verified:', $('#otp_verified').val());

        // Force submit the form directly
        $('.wallet-form')[0].submit();
    });

    // BEP20 Edit button functionality
    $('#editBep20Btn').on('click', function() {
        const bitcoinInput = $('#bitcoin_input');
        const userEmail = '<?php echo $row->email; ?>';

        // Store original value
        bitcoinInput.attr('data-original', bitcoinInput.val());

        // Send OTP immediately when edit is clicked
        $.ajax({
            url: 'send_otp.php',
            type: 'POST',
            data: {
                email: userEmail,
                type: 'bep20_update'
            },
            success: function(response) {
                const result = JSON.parse(response);
                if (result.success) {
                    $('#modalBep20Otp').modal('show');
                    $('#otpMessage').text('OTP sent to your email: ' + userEmail).css('color', '#28a745');
                } else {
                    alert('Failed to send OTP: ' + result.message);
                }
            },
            error: function() {
                alert('Error sending OTP. Please try again.');
            }
        });
    });



    // Resend OTP functionality
    $('#resendOtpBtn').on('click', function() {
        const userEmail = '<?php echo $row->email; ?>';

        $.ajax({
            url: 'send_otp.php',
            type: 'POST',
            data: {
                email: userEmail,
                type: 'bep20_update'
            },
            success: function(response) {
                const result = JSON.parse(response);
                if (result.success) {
                    $('#otpMessage').text('OTP resent to your email: ' + userEmail).css('color', '#28a745');
                } else {
                    $('#otpMessage').text('Failed to resend OTP: ' + result.message).css('color', '#ff0000');
                }
            },
            error: function() {
                $('#otpMessage').text('Error resending OTP. Please try again.').css('color', '#ff0000');
            }
        });
    });

    // Verify OTP and enable editing
    $('#verifyOtpBtn').on('click', function() {
        const otp = $('#bep20_otp').val().trim();
        const userEmail = '<?php echo $row->email; ?>';

        if (!otp) {
            $('#otpMessage').text('Please enter OTP code').css('color', '#ff0000');
            return;
        }

        $.ajax({
            url: 'verify_otp.php',
            type: 'POST',
            data: {
                email: userEmail,
                otp: otp,
                type: 'bep20_update'
            },
            success: function(response) {
                const result = JSON.parse(response);
                if (result.success) {
                    console.log('OTP verification successful');
                    $('#otp_verified').val('true');
                    $('#modalBep20Otp').modal('hide');

                    // Enable the input field for editing
                    const bitcoinInput = $('#bitcoin_input');
                    bitcoinInput.prop('readonly', false);
                    bitcoinInput.focus();

                    // Hide edit button
                    $('#editBep20Btn').hide();
                    $('#sendOtpBtn').hide();
                    // Enable the save button
                    console.log('Button disabled before:', $('#saveBtn').prop('disabled'));
                    $('#saveBtn').prop('disabled', false);
                    $('#saveBtn').removeAttr('disabled');
                    console.log('Button disabled after:', $('#saveBtn').prop('disabled'));
                    console.log('Button HTML:', $('#saveBtn')[0].outerHTML);

                    // Show success message and ask user to edit address
                    alert('OTP verified successfully! Please edit your BEP20 address and then click Save.');
                } else {
                    $('#otpMessage').text('Invalid OTP. Please try again.').css('color', '#ff0000');
                }
            },
            error: function() {
                $('#otpMessage').text('Error verifying OTP. Please try again.').css('color', '#ff0000');
            }
        });
    });


});





/* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content - This allows the user to have multiple dropdowns without any conflict */
var dropdown = document.getElementsByClassName("dropdown-btn");
var i;

for (i = 0; i < dropdown.length; i++) {
  dropdown[i].addEventListener("click", function() {
    this.classList.toggle("active");
    this.classList.toggle("dactive");
    var dropdownContent = this.nextElementSibling;
    if (dropdownContent.style.display === "block") {
      dropdownContent.style.display = "none";
    } else {
      dropdownContent.style.display = "block";
    }
  });
}
</script>

</div>
</div>
</div>
</div>
</div>
</body>
</html>
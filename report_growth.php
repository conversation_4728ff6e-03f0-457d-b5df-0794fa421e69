<?php
$type = (isset($_GET['type']) && (int) $_GET['type'] <= 6) ? (int) $_GET['type'] : 0;
$title = ($type == 2) ? "Super Jackpot Income" : (($type == 1) ? "Stacking ROI Income" : "Trade Bonus");
include_once 'header.php';
$query = "SELECT g.*, ip.title FROM income_growth as g"
        . " LEFT JOIN investments as i ON i.recid=g.iid"
        . " LEFT JOIN investments_plan as ip ON ip.recid=i.ipid"
        . " WHERE g.uid='".$uid."' AND g.type=".$type
        . " ORDER BY g.datetime DESC";
$result = my_query($query);
$i=0;

// Calculate total ROI earnings
$total_earnings_query = "SELECT SUM(amount) as total FROM income_growth WHERE uid='$uid' AND type=$type";
$total_earnings_result = my_query($total_earnings_query);
$total_earnings_row = mysqli_fetch_object($total_earnings_result);
$total_earnings = $total_earnings_row->total ? $total_earnings_row->total : 0;

// Calculate average percentage
$avg_percentage_query = "SELECT AVG(percentage) as avg_percentage FROM income_growth WHERE uid='$uid' AND type=$type";
$avg_percentage_result = my_query($avg_percentage_query);
$avg_percentage_row = mysqli_fetch_object($avg_percentage_result);
$avg_percentage = $avg_percentage_row->avg_percentage ? round($avg_percentage_row->avg_percentage, 2) : 0;

// Get total number of payments
$total_payments = mysqli_num_rows($result);

// Get latest payment date
$latest_payment_query = "SELECT MAX(datetime) as latest FROM income_growth WHERE uid='$uid' AND type=$type";
$latest_payment_result = my_query($latest_payment_query);
$latest_payment_row = mysqli_fetch_object($latest_payment_result);
$latest_payment = $latest_payment_row->latest ? date("d M, Y", strtotime($latest_payment_row->latest)) : 'N/A';
?>

<style>
    body, #page-wrapper {
        background-color: #0b0e11;
    }
    .content-header {
        display: none;
    }

    .roi-wrapper {
        padding: 15px;
        color: #eaecef;
        margin: 0 auto;
        /*max-width: 1200px;*/
    }

    /* ROI Header */
    .roi-header {
        background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
        border-radius: 12px;
        padding: 25px 30px;
        margin-bottom: 25px;
        position: relative;
        overflow: hidden;
        animation: slideInFade 0.8s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .roi-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #f0b90b, transparent);
    }

    .roi-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100px;
        background: radial-gradient(ellipse at bottom, rgba(240, 185, 11, 0.1), transparent 70%);
        pointer-events: none;
    }

    .roi-title {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .roi-title h2 {
        font-size: 24px;
        color: #fff;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .roi-title h2 i {
        margin-right: 12px;
        color: #f0b90b;
    }

    .roi-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 15px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
        z-index: -1;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.08);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }

    .stat-label {
        font-size: 14px;
        color: #848e9c;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .stat-label i {
        margin-right: 8px;
        color: #f0b90b;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #fff;
        display: flex;
        align-items: baseline;
    }

    .stat-value.earnings {
        color: #0ecb81;
    }

    .stat-value .currency {
        font-size: 14px;
        margin-right: 4px;
        opacity: 0.7;
    }

    .stat-value .unit {
        font-size: 14px;
        margin-left: 4px;
        opacity: 0.7;
    }

    /* ROI Card */
    .roi-card {
        background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
        border-radius: 12px;
        margin-bottom: 25px;
        overflow: hidden;
        animation: slideUp 0.6s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.05);
        position: relative;
    }

    .roi-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, #f0b90b, transparent);
    }

    .card-header {
        background: rgba(0, 0, 0, 0.2);
        padding: 15px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-header h3 {
        margin: 0;
        font-size: 16px;
        color: #f0b90b;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .card-header h3 i {
        margin-right: 10px;
    }

    .card-header-actions {
        display: flex;
        gap: 10px;
    }

    .card-filter {
        background: rgba(255, 255, 255, 0.05);
        border: none;
        color: #eaecef;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .card-filter:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .card-body {
        padding: 0;
    }

    /* Table Styling */
    .roi-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .roi-table th {
        background: rgba(0, 0, 0, 0.2);
        color: #848e9c;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .roi-table td {
        padding: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        color: #eaecef;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .roi-table tr:last-child td {
        border-bottom: none;
    }

    .roi-table tr:hover td {
        background: rgba(255, 255, 255, 0.03);
    }

    /* Plan Badge */
    .plan-badge {
        display: inline-flex;
        align-items: center;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        background-color: rgba(240, 185, 11, 0.1);
        color: #f0b90b;
        border: 1px solid rgba(240, 185, 11, 0.2);
    }

    /* Percentage Badge */
    .percentage-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 50px;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 13px;
        font-weight: 600;
        background-color: rgba(14, 203, 129, 0.1);
        color: #0ecb81;
    }

    /* Amount Value */
    .amount-value {
        font-weight: 600;
        color: #f0b90b;
    }

    /* Month Badge */
    .month-badge {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: linear-gradient(45deg, #f0b90b, #f8d33a);
        color: #0b0e11;
        font-weight: bold;
        font-size: 12px;
    }

    /* Pagination */
    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        padding: 10px;
    }

    .pagination {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        overflow: hidden;
    }

    .pagination li {
        border-right: 1px solid rgba(255, 255, 255, 0.05);
    }

    .pagination li:last-child {
        border-right: none;
    }

    .pagination a {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        color: #eaecef;
        text-decoration: none;
        transition: all 0.2s ease;
        min-width: 40px;
    }

    .pagination a:hover {
        background: rgba(255, 255, 255, 0.05);
    }

    .pagination .active a {
        background: rgba(240, 185, 11, 0.2);
        color: #f0b90b;
    }

    /* Empty State */
    .empty-state {
        padding: 40px 20px;
        text-align: center;
        color: #848e9c;
    }

    .empty-icon {
        font-size: 48px;
        margin-bottom: 20px;
        color: rgba(240, 185, 11, 0.3);
    }

    .empty-text {
        font-size: 16px;
        margin-bottom: 15px;
    }

    .empty-subtext {
        font-size: 14px;
        opacity: 0.7;
        max-width: 400px;
        margin: 0 auto;
    }

    /* Responsive Adjustments */
    @media (max-width: 992px) {
        .roi-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 768px) {
        .roi-header {
            padding: 20px;
        }

        .roi-table {
            display: block;
            overflow-x: auto;
        }

        .roi-stats {
            grid-template-columns: 1fr;
        }

        .card-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .card-header-actions {
            width: 100%;
            justify-content: space-between;
        }
    }

    /* Animations */
    @keyframes slideInFade {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes slideUp {
        from {
            transform: translateY(20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(240, 185, 11, 0.4);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(240, 185, 11, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(240, 185, 11, 0);
        }
    }
</style>
<div class="roi-wrapper">
    <!-- ROI Header with Stats -->
    <div class="roi-header">
        <div class="roi-title">
            <h2><i class="fas fa-chart-line"></i> <?php echo $title; ?></h2>
        </div>
        <div class="roi-stats">
            <div class="stat-card">
                <div class="stat-label"><i class="fas fa-coins"></i> Total Earnings</div>
                <div class="stat-value earnings"><span class="currency"><?php echo SITE_CURRENCY; ?></span><?php echo number_format($total_earnings, 2); ?></div>
            </div>
            <div class="stat-card">
                <div class="stat-label"><i class="fas fa-percentage"></i> Average ROI</div>
                <div class="stat-value"><?php echo $avg_percentage; ?><span class="unit">%</span></div>
            </div>
            <div class="stat-card">
                <div class="stat-label"><i class="fas fa-calendar-check"></i> Total Payments</div>
                <div class="stat-value"><?php echo $total_payments; ?></div>
            </div>
            <div class="stat-card">
                <div class="stat-label"><i class="fas fa-clock"></i> Latest Payment</div>
                <div class="stat-value" style="font-size: 18px;"><?php echo $latest_payment; ?></div>
            </div>
        </div>
    </div>

    <!-- ROI History Card -->
    <div class="roi-card">
        <div class="card-header">
            <h3><i class="fas fa-history"></i> Payment History</h3>
            <div class="card-header-actions">
                <select class="card-filter" onchange="window.location.href='roi.php?type='+this.value">
                    <option value="0" <?php if($type == 0) echo 'selected'; ?>>ROI Income</option>
                    <option value="1" <?php if($type == 1) echo 'selected'; ?>>Stacking ROI</option>
                    <option value="2" <?php if($type == 2) echo 'selected'; ?>>Super Jackpot</option>
                </select>
            </div>
        </div>
        <div class="card-body">
            <table class="roi-table">
                <thead>
                    <tr>
                        <th width="50">#</th>
                        <th>Amount</th>
                        <th>Days</th>
                        <th>Date</th>
                        <th>ROI</th>
                        <th>%</th>
                        <?php /*<th>Basic %</th>
                        <th>Hold %</th>
                        <th>Contract %</th>
                        <th>Personal %</th>
                        <th>Community %</th>
                        <th>Package (<?php echo SITE_CURRENCY;?>)</th>*/?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php while ($row = mysqli_fetch_object($result)){$i++;?>
                            <tr>
                                <td><?php echo $i;?></td>
                                <td><span class="plan-badge"><?php echo $row->iamount;?></span></td>
                                <td><div class="month-badge"><?php echo $row->days;?></div></td>
                                <td><?php echo date("d M, Y", strtotime($row->datetime));?></td>
                                <td><span class="amount-value"><?php echo number_format($row->amount*1, 2);?></span></td>
                                <td><span class="percentage-badge"><?php echo $row->percentage;?>%</span></td>
                                <?php /*<td><?php echo $row->p1;?></td>
                                <td><?php echo $row->p2;?></td>
                                <td><?php echo $row->p3;?></td>
                                <td><?php echo $row->p4;?></td>
                                <td><?php echo $row->p5;?></td>
                                <td><?php echo $row->iamount*1;?></td>*/?>
                            </tr>
                            <?php }?>
                        </tbody>
                    </table>

                    <!-- Empty state (will only show if there are no payments) -->
                    <?php if ($total_payments == 0): ?>
                    <div class="empty-state">
                        <div class="empty-icon"><i class="fas fa-coins"></i></div>
                        <div class="empty-text">No payments found</div>
                        <div class="empty-subtext">You don't have any <?php echo strtolower($title); ?> payments yet</div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_payments > 0): ?>
                <div class="pagination-container">
                    <ul class="pagination">
                        <li><a href="#"><i class="fas fa-angle-double-left"></i></a></li>
                        <li><a href="#"><i class="fas fa-angle-left"></i></a></li>
                        <li class="active"><a href="#">1</a></li>
                        <li><a href="#">2</a></li>
                        <li><a href="#">3</a></li>
                        <li><a href="#"><i class="fas fa-angle-right"></i></a></li>
                        <li><a href="#"><i class="fas fa-angle-double-right"></i></a></li>
                    </ul>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php include_once 'footer.php'; ?>
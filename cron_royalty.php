<?php include_once '../lib/config.php';
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(-1);
$tmarr = array(0, 20, 40, 60, 120, 500, 1000);
$dmarr = array(0, 2, 3, 5, 10, 50, 100);
$time_arr = array(0, 15, 30, 50, 70, 100, 150);
$amtarr = array(0, 30, 50, 70, 100, 500, 1000);
$amtarr2 = array(0, 5, 6, 7, 15, 30);
$team_arr = array(0, 2, 4, 6, 15, 30);

$date = date('Y-m-15');

$result = mysqli_query($link, "SELECT * FROM user WHERE topup > 0 AND reward <= 10");
while ($row = mysqli_fetch_object($result)) {
    $uid = $row->uid;
    $reward = $row->reward;
    
    $_l = 0;
    $new_amount = 0;
    if($row->teamc >= 21070){
        $_l = 9;
        $new_amount = 6000;
    }
    elseif($row->teamc >= 11070){
        $_l = 8;
        $new_amount = 3000;
    }
    elseif($row->teamc >= 5070){
        $_l = 7;
        $new_amount = 1500;
    }
    elseif($row->teamc >= 2070){
        $_l = 6;
        $new_amount = 600;
    }
    elseif($row->teamc >= 1070){
        $_l = 5;
        $new_amount = 400;
    }
    elseif($row->teamc >= 470){
        $_l = 4;
        $new_amount = 300;
    }
    elseif($row->teamc >= 170){
        $_l = 3;
        $new_amount = 200;
    }
    elseif($row->teamc >= 70){
        $_l = 2;
        $new_amount = 125;
    }
    elseif($row->teamc >= 20){
        $_l = 1;
        $new_amount = 70;
    }
    
    if($_l > 0){
        royalty_insert($uid, $new_amount, $_l, 0);
    }
    
    // foreach($tmarr as $k => $v){
    //     if($k){
    //         $datetime_bs = date('Y-m-d H:i:s', strtotime('+'.$v.' days', strtotime($row->datetime)));
    //         $check_d = my_num_rows(my_query("SELECT uid FROM user WHERE refer_id='".$uid."' AND status = 0 AND topup > 0 AND topup_datetime <= '".$datetime_bs."' "));
    //         if($check_d >= $v){
    //             $new_amount = $amtarr2[$k];
    //             royalty_insert($uid, $new_amount, $k, 1);
    //         }
    //     }
    // }
    
    foreach($tmarr as $k => $v){
        if($k){
            $days = $time_arr[$k];
            $datetime_bs = date('Y-m-d H:i:s', strtotime('+'.$days.' days', strtotime($row->datetime)));
            $check_d = my_num_rows(my_query("SELECT uid FROM user WHERE refer_id='".$uid."' AND status = 0 AND topup > 0 AND topup_datetime <= '".$datetime_bs."' "));
           if($row->teamc >= $v && $check_d >= $dmarr[$k]){
                $new_amount = $amtarr[$k];
                royalty_insert($uid, $new_amount, $k, 2);
            }
        }
    }
}

function royalty_insert($uid, $new_amount, $l, $type = 0, $n = 0, $tamt = 0){
    $new_amount = check_3x($uid, $new_amount);

    if($type == 2 && $new_amount > 0){
        $check = my_num_rows(my_query("SELECT uid FROM income_royalty WHERE uid='".$uid."' AND type = '".$type."' AND level = '".$l."'"));
        if(!$check){
            my_query("UPDATE user SET wallet= wallet+'$new_amount' WHERE uid='".$uid."'");
            my_query("INSERT INTO `income_royalty` (`uid`, `amount`, `datetime`, `level`, `type`) VALUES ('".$uid ."', '".$new_amount."', '".date('c')."', '".$l."', '".$type."')");
        }
    }
    elseif($type == 1 && $new_amount > 0){
        $check = my_num_rows(my_query("SELECT uid FROM income_royalty WHERE uid='".$uid."' AND type = '".$type."' AND level = '".$l."'"));
        if(!$check){
            my_query("UPDATE user SET wallet= wallet+'$new_amount' WHERE uid='".$uid."'");
            my_query("INSERT INTO `income_royalty` (`uid`, `amount`, `datetime`, `level`, `type`) VALUES ('".$uid ."', '".$new_amount."', '".date('c')."', '".$l."', '".$type."')");
        }
    }
    elseif($type == 0 && $new_amount > 0){
        $check = my_num_rows(my_query("SELECT uid FROM income_royalty WHERE uid='".$uid."' AND type = '".$type."' AND level = '".$l."'"));
        if(!$check){
            $days = $check+1;
            my_query("UPDATE user SET wallet= wallet+'$new_amount' WHERE uid='".$uid."'");
            my_query("INSERT INTO `income_royalty` (`uid`, `amount`, `datetime`, `level`, `type`, tamt, tid, days) VALUES ('".$uid ."', '".$new_amount."', '".date('c')."', '".$l."', '".$type."', '".($tamt)."', '".$n."', '".$days."')");
        }
    }
}

// Function to process weekly direct referral rewards
function process_weekly_rewards() {
    global $link;

    $current_week_start = date('Y-m-d', strtotime('monday this week'));
    $current_week_end = date('Y-m-d', strtotime('sunday this week'));

    // Get all active users with topup > 0
    $weekly_result = mysqli_query($link, "SELECT * FROM user WHERE topup > 0 AND status = 0");
    while ($weekly_row = mysqli_fetch_object($weekly_result)) {
        $uid = $weekly_row->uid;

        // Count NEW direct referrals made DURING this week (registered this week with topup > 0)
        $direct_count_query = "SELECT COUNT(*) as direct_count FROM user WHERE refer_id = '".$uid."' AND topup > 0 AND status = 0 AND DATE(datetime) >= '".$current_week_start."' AND DATE(datetime) <= '".$current_week_end."'";
        $direct_result = mysqli_query($link, $direct_count_query);
        $direct_data = mysqli_fetch_object($direct_result);
        $direct_count = $direct_data->direct_count;

        // Check which weekly reward tier the user qualifies for
        $weekly_reward_amount = 0;
        $reward_level = 0;

        if($direct_count >= 25) {
            $weekly_reward_amount = 300;
            $reward_level = 3; // Level 3 for 25+ NEW direct this week
        }
        elseif($direct_count >= 10) {
            $weekly_reward_amount = 100;
            $reward_level = 2; // Level 2 for 10+ NEW direct this week
        }
        elseif($direct_count >= 5) {
            $weekly_reward_amount = 50;
            $reward_level = 1; // Level 1 for 5+ NEW direct this week
        }

        // If user qualifies for weekly reward
        if($weekly_reward_amount > 0) {
            // Check if user already received this week's reward for this level
            $check_weekly_reward = my_num_rows(my_query("SELECT recid FROM income_reward WHERE uid='".$uid."' AND reward='".$reward_level."' AND type=1 AND datetime >= '".$current_week_start." 00:00:00' AND datetime <= '".$current_week_end." 23:59:59'"));

            if(!$check_weekly_reward) {
                // Apply 3x limit check if function exists
                if(function_exists('check_3x')) {
                    $weekly_reward_amount = check_3x($uid, $weekly_reward_amount);
                }

                if($weekly_reward_amount > 0) {
                    // Add reward to user wallet
                    my_query("UPDATE user SET wallet = wallet + '".$weekly_reward_amount."' WHERE uid='".$uid."'");

                    // Insert record into income_reward table
                    my_query("INSERT INTO income_reward (uid, reward, datetime, type, status) VALUES ('".$uid."', '".$reward_level."', '".date('Y-m-d H:i:s')."', 1, 1)");
                }
            }
        }
    }
}

// Process weekly rewards (uncomment the line below to enable weekly rewards in this cron)
// process_weekly_rewards();

//if($date == date('Y-m-d')){
    //my_query("UPDATE investments SET statusc=1 WHERE statusc=0");
//}
echo "<br/> Closing complete. Please close this browser.";
?>
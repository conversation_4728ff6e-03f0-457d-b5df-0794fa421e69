# Tour Eligibility System - Summary

## ✅ What It Does

The system **marks users as eligible for Europe Tour** based on their team business performance. **No monetary rewards** are given - only eligibility tracking.

## 🎯 Eligibility Requirements

- **Total Tour Business**: $10,000
- **Calculation Method**: 50% from strongest direct + 50% from all other directs
- **Processing**: Daily in `cron_tb.php`

## 🔄 How It Works

### 1. **Find Direct Users**
Gets all direct referrals of each user who are active (status=0, topup>0)

### 2. **Calculate Team Business for Each Direct**
For each direct user:
- **Own Business**: Sum of their investments
- **Downline Business**: Recursive calculation of all their referrals
- **Total**: Own + Downline

### 3. **Apply 50/50 Logic**
- **Strongest Direct**: Take 50% from the direct user with highest team business
- **Other Directs**: Take 50% from sum of all other direct users' team business
- **Tour Business**: Add both together

### 4. **Check Eligibility**
If Tour Business ≥ $10,000 → Mark as eligible for tour

### 5. **Record Eligibility**
Store in `income_reward` table:
- `reward = 99` (Tour eligibility identifier)
- `type = 2` (Special eligibility type)
- No monetary amount - just tracking

## 📊 Example

**User has 3 direct referrals:**
- **Direct A**: $5,000 own + $10,000 downline = $15,000 total
- **Direct B**: $3,000 own + $5,000 downline = $8,000 total  
- **Direct C**: $2,000 own + $3,000 downline = $5,000 total

**Tour Business Calculation:**
- Strongest Direct (50%): $15,000 × 0.5 = $7,500
- Other Directs (50%): ($8,000 + $5,000) × 0.5 = $6,500
- **Total Tour Business**: $7,500 + $6,500 = $14,000

**Result**: ✅ **Eligible for Europe Tour** ($14,000 ≥ $10,000)

## 🗄️ Database Storage

**Only in `income_reward` table:**
```sql
INSERT INTO income_reward (uid, reward, datetime, type, status) 
VALUES ('123', 99, '2025-01-20 10:30:00', 2, 1);
```

**No other tables modified** - clean implementation!

## 🔍 Check Eligible Users

```sql
SELECT ir.*, u.login_id, u.name
FROM income_reward ir
JOIN user u ON ir.uid = u.uid
WHERE ir.reward = 99 AND ir.type = 2
ORDER BY ir.datetime DESC;
```

## ⏰ Processing

**Daily Cron** (`cron_tb.php`):
```bash
0 2 * * * /usr/bin/php /path/to/cron_tb.php
```

## 📈 Expected Output

```
Processing user UID: 123
Direct User 101: Team Business = $15,000
Direct User 102: Team Business = $8,000  
Direct User 103: Team Business = $5,000

Tour Business: $14,000 (Strongest: $7,500 + Others: $6,500)

✅ Tour Reward (Europe Tour) ELIGIBILITY recorded for user UID: 123
```

## 🎉 Key Features

✅ **Pure eligibility tracking** - No monetary rewards
✅ **Real team business calculation** - Not using existing teamb field
✅ **Proper 50/50 logic** - Strongest vs others
✅ **Daily processing** - Immediate eligibility check
✅ **Duplicate prevention** - One record per user
✅ **Clean database** - Only final eligibility records
✅ **No table modifications** - Uses existing structure

## 🚀 Ready to Use!

The system will:
1. Calculate proper team business for each direct user
2. Apply the 50% strongest + 50% others logic
3. Check if total ≥ $10,000
4. Mark eligible users in `income_reward` table
5. Provide clear logging of eligibility

**Test it**: Run `php cron_tb.php` to see tour eligibility processing in action!

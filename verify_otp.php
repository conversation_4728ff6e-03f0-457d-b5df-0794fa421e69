<?php
session_start();

$email = $_POST['email'] ?? '';
$otp = $_POST['otp'] ?? '';
$type = $_POST['type'] ?? 'register';

if (
    isset($_SESSION['otp_email'], $_SESSION['otp']) &&
    $_SESSION['otp_email'] === strtolower($email) &&
    $_SESSION['otp'] == $otp &&
    $_SESSION['otp_type'] === $type
) {
    // OTP verified, you can unset the session if you want
    // unset($_SESSION['otp']); // Optionally keep for register_model.php
    // unset($_SESSION['otp_email']);
    // unset($_SESSION['otp_type']);
    echo json_encode(['success' => true]);
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid OTP']);
}
?>

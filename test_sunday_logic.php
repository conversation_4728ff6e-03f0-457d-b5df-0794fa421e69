<?php
// Test script to show Sunday to Sunday logic

echo "<h3>Sunday to Sunday Weekly Logic Test</h3>";

// Current date logic (as if running on Sunday)
$current_week_start = date('Y-m-d', strtotime('sunday -6 days')); // Last Sunday (7 days ago)
$current_week_end = date('Y-m-d'); // Today (Sunday)

echo "<strong>If cron runs TODAY (" . date('Y-m-d l') . "):</strong><br/>";
echo "Week Start (Last Sunday): " . $current_week_start . "<br/>";
echo "Week End (This Sunday): " . $current_week_end . "<br/>";
echo "Days covered: 7 days (Sunday to Sunday)<br/><br/>";

// Show what happens on different days
$test_dates = [
    '2025-01-19', // Sunday
    '2025-01-20', // Monday  
    '2025-01-21', // Tuesday
    '2025-01-25', // Saturday
    '2025-01-26', // Next Sunday
];

echo "<strong>Testing different run dates:</strong><br/>";
foreach($test_dates as $test_date) {
    $day_name = date('l', strtotime($test_date));
    $week_start = date('Y-m-d', strtotime('sunday -6 days', strtotime($test_date)));
    $week_end = date('Y-m-d', strtotime($test_date));
    
    echo "If run on $test_date ($day_name):<br/>";
    echo "&nbsp;&nbsp;Week: $week_start to $week_end<br/>";
    echo "&nbsp;&nbsp;Checks: NEW referrals registered between these dates<br/><br/>";
}

echo "<strong>SQL Query Example:</strong><br/>";
echo "<code>";
echo "SELECT COUNT(*) as direct_count<br/>";
echo "FROM user<br/>";
echo "WHERE refer_id = 'USER_ID'<br/>";
echo "AND topup > 0<br/>";
echo "AND status = 0<br/>";
echo "AND DATE(datetime) >= '$current_week_start'<br/>";
echo "AND DATE(datetime) <= '$current_week_end'";
echo "</code><br/><br/>";

echo "<strong>Reward Logic:</strong><br/>";
echo "• If NEW direct count >= 5 → $50 reward<br/>";
echo "• If NEW direct count >= 10 → $100 reward<br/>";
echo "• If NEW direct count >= 25 → $300 reward<br/>";
echo "• Only counts users who registered AND activated (topup > 0) during the week<br/>";

echo "<br/><strong>Recommended Cron Schedule:</strong><br/>";
echo "Run every Sunday at 11:59 PM:<br/>";
echo "<code>59 23 * * 0 /usr/bin/php /path/to/cron_weekly_rewards.php</code>";
?>

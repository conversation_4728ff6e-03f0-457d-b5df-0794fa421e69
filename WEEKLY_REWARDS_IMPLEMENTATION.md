# Weekly Direct Referral Rewards Implementation

## Overview
This implementation adds a weekly reward system based on **NEW direct referrals made during that specific week**:

- **5 NEW Direct ID in a week**: $50 Weekly
- **10 NEW Direct ID in a week**: $100 Weekly
- **25 NEW Direct ID in a week**: $300 Weekly

**Important**: Rewards are based on NEW referrals made during the current week, not total active referrals.

### Example Scenario:
- **Week 1**: User refers 5 new people → ✅ Gets $50 reward
- **Week 2**: User refers 0 new people (but still has 5 active from Week 1) → ❌ Gets no reward
- **Week 3**: User refers 3 new people → ❌ Gets no reward (less than 5 new)
- **Week 4**: User refers 7 new people → ✅ Gets $50 reward (5+ new referrals)

## Files Created/Modified

### 1. `cron_weekly_rewards.php` (NEW)
- **Purpose**: Dedicated weekly cron job for processing weekly rewards
- **Recommended Usage**: Schedule this to run weekly (e.g., every Monday)
- **Features**:
  - Detailed logging and reporting
  - Summary statistics
  - Prevents duplicate rewards for the same week
  - Applies 3x limit checks if available

### 2. `cron_royalty.php` (MODIFIED)
- **Added**: `process_weekly_rewards()` function
- **Status**: Function is commented out by default
- **Usage**: Uncomment the function call if you want to include weekly rewards in your existing cron

## Database Integration

### `income_reward` Table Usage
The system uses your existing `income_reward` table with the following mapping:

| Field | Usage |
|-------|-------|
| `recid` | Auto-increment primary key |
| `uid` | User ID receiving the reward |
| `reward` | Reward level (1=5 direct, 2=10 direct, 3=25 direct) |
| `datetime` | When the reward was given |
| `type` | Set to `1` for weekly direct referral rewards |
| `status` | Set to `1` for active/completed rewards |

### User Wallet Update
- Rewards are automatically added to the user's `wallet` field in the `user` table
- Uses existing `check_3x()` function if available to apply limits

## How It Works

### 1. NEW Direct Referral Counting (Weekly)
```sql
SELECT COUNT(*) as direct_count
FROM user
WHERE refer_id = '$uid'
AND topup > 0
AND status = 0
AND DATE(datetime) >= '$current_week_start'
AND DATE(datetime) <= '$current_week_end'
```
- Only counts **NEW users registered THIS WEEK**
- Only counts **active users** (status = 0)
- Only counts users with **topup > 0** (activated users)
- Uses registration date (`datetime` field) to determine if user was referred this week

### 2. Reward Tiers (Based on NEW Weekly Referrals)
- **Level 1**: 5+ NEW direct referrals this week = $50
- **Level 2**: 10+ NEW direct referrals this week = $100
- **Level 3**: 25+ NEW direct referrals this week = $300

### 3. Duplicate Prevention
- Checks if user already received reward for current week
- Uses date range: Monday to Sunday of current week
- Prevents multiple rewards for same tier in same week

### 4. 3x Limit Integration
- Applies existing `check_3x()` function if available
- Ensures rewards comply with your existing limit system

## Setup Instructions

### Option 1: Dedicated Weekly Cron (Recommended)
1. **Schedule `cron_weekly_rewards.php`** to run weekly:
   ```bash
   # Run every Monday at 9:00 AM
   0 9 * * 1 /usr/bin/php /path/to/your/cron_weekly_rewards.php
   ```

2. **Benefits**:
   - Separate logging and monitoring
   - Better performance (doesn't slow down daily cron)
   - Detailed reporting and statistics

### Option 2: Include in Existing Cron
1. **Edit `cron_royalty.php`**:
   - Uncomment line 172: `// process_weekly_rewards();`
   - Change to: `process_weekly_rewards();`

2. **Benefits**:
   - Single cron job to manage
   - Integrated with existing reward system

## Testing

### Manual Testing
1. **Run the weekly cron manually**:
   ```bash
   php cron_weekly_rewards.php
   ```

2. **Check output** for:
   - Users processed
   - Rewards given
   - Any errors or warnings

### Database Verification
1. **Check `income_reward` table**:
   ```sql
   SELECT * FROM income_reward WHERE type = 1 ORDER BY datetime DESC;
   ```

2. **Check user wallets**:
   ```sql
   SELECT uid, wallet FROM user WHERE uid IN (SELECT uid FROM income_reward WHERE type = 1);
   ```

## Monitoring and Logs

### Weekly Cron Output
The `cron_weekly_rewards.php` provides detailed output:
- ✓ Successful rewards
- ⚠ Blocked by limits  
- ℹ Already received rewards
- Summary statistics

### Example Output
```
✓ Weekly reward of $50 given to user USER123 (UID: 456) - Direct count: 7 - 5 Direct ID - Weekly Reward
ℹ User USER456 (UID: 789) already received weekly reward for this week - Direct count: 12
⚠ User USER789 (UID: 101) qualified but reward blocked by 3x limit - Direct count: 15

=== Weekly Rewards Summary ===
Total users rewarded: 25
Total rewards given: $1,250
Processing completed at: 2025-01-20 09:00:15
```

## Security Features

### Duplicate Prevention
- Week-based checking prevents multiple rewards
- Uses Monday-Sunday week boundaries
- Level-specific checking (user can't get same level twice in one week)

### Validation
- Only active users (status = 0) qualify
- Only users with topup > 0 qualify  
- Integrates with existing 3x limit system
- SQL injection protection through existing functions

## Customization

### Changing Reward Amounts
Edit the reward amounts in the files:
```php
if($direct_count >= 25) {
    $weekly_reward_amount = 300; // Change this value
    $reward_level = 3;
}
```

### Changing Qualification Criteria
Modify the direct referral counting query to add additional criteria:
```php
$direct_count_query = "SELECT COUNT(*) as direct_count 
                      FROM user 
                      WHERE refer_id = '".$uid."' 
                      AND topup > 0 
                      AND status = 0
                      AND additional_criteria = 'value'"; // Add your criteria
```

## Troubleshooting

### Common Issues
1. **No rewards given**: Check if users meet qualification criteria
2. **Duplicate rewards**: Verify week boundary logic
3. **Missing functions**: Ensure `../lib/config.php` is properly included

### Debug Mode
Add debug output to see qualification details:
```php
echo "User $uid: Direct count = $direct_count, Qualifies = " . ($weekly_reward_amount > 0 ? 'Yes' : 'No') . "<br/>";
```

## Support
- All functions use your existing database helper functions (`my_query`, `my_num_rows`)
- Integrates with existing reward limit system (`check_3x`)
- Uses existing database structure (no new tables required)

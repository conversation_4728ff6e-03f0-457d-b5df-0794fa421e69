<?php
echo "<h1>Form Submission Test</h1>";

echo "<h2>POST Data Received:</h2>";
if (!empty($_POST)) {
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
} else {
    echo "<p>No POST data received</p>";
}

echo "<h2>Test Form:</h2>";
?>
<form action="update_wallet_model.php" method="post">
    <input type="hidden" name="uid" value="1" />
    <input type="hidden" name="otp_verified" value="false" />
    <label>Bitcoin Address:</label>
    <input type="text" name="bitcoin" value="******************************************" />
    <br><br>
    <button type="submit">Test Submit</button>
</form>

<h2>Instructions:</h2>
<ol>
    <li>Open browser console (F12)</li>
    <li>Go to profile.php</li>
    <li>Try to submit the BEP20 form</li>
    <li>Check console logs for debugging information</li>
    <li>Check if update_wallet_model.php is being called</li>
</ol>

<h2>Debugging Steps:</h2>
<ul>
    <li>Check if jQuery is loaded: <code>console.log(typeof $)</code></li>
    <li>Check if wallet form exists: <code>console.log($('.wallet-form').length)</code></li>
    <li>Check form action: <code>console.log($('.wallet-form').attr('action'))</code></li>
    <li>Test form submission manually: <code>$('.wallet-form')[0].submit()</code></li>
</ul>

<?php
/**
 * Test file for BEP20 address validation
 */

// Include the validation functions
include_once 'validate_bep20.php';

// Test cases for BEP20 address validation
$test_addresses = [
    // Valid BEP20 addresses
    '0x1234567890123456789012345678901234567890' => true,
    '0xabcdefABCDEF1234567890123456789012345678' => true,
    '0x0000000000000000000000000000000000000000' => true,
    
    // Invalid BEP20 addresses
    '1234567890123456789012345678901234567890' => false,  // Missing 0x prefix
    '0x123456789012345678901234567890123456789' => false,   // Too short (41 chars)
    '0x12345678901234567890123456789012345678901' => false, // Too long (43 chars)
    '0x123456789012345678901234567890123456789g' => false,  // Invalid hex character
    '' => false,                                             // Empty string
    '0x' => false,                                          // Only prefix
];

echo "<h2>BEP20 Address Validation Test Results</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Address</th><th>Expected</th><th>Result</th><th>Status</th></tr>";

$passed = 0;
$total = count($test_addresses);

foreach ($test_addresses as $address => $expected) {
    $result = validateBEP20Address($address);
    $status = ($result === $expected) ? '✅ PASS' : '❌ FAIL';
    
    if ($result === $expected) {
        $passed++;
    }
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($address) . "</td>";
    echo "<td>" . ($expected ? 'Valid' : 'Invalid') . "</td>";
    echo "<td>" . ($result ? 'Valid' : 'Invalid') . "</td>";
    echo "<td>$status</td>";
    echo "</tr>";
}

echo "</table>";
echo "<br><strong>Test Summary: $passed/$total tests passed</strong>";

if ($passed === $total) {
    echo "<br><span style='color: green;'>✅ All tests passed! BEP20 validation is working correctly.</span>";
} else {
    echo "<br><span style='color: red;'>❌ Some tests failed. Please check the validation logic.</span>";
}
?>

<h2>Implementation Summary</h2>
<div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin-top: 20px;">
    <h3>What has been implemented:</h3>
    <ul>
        <li><strong>BEP20 Address Validation:</strong> Validates that addresses are 42 characters long, start with '0x', and contain only hexadecimal characters</li>
        <li><strong>First-time vs Update Logic:</strong> Users can set BEP20 address first time without OTP, but subsequent updates require OTP verification</li>
        <li><strong>OTP Integration:</strong> Uses existing send_otp.php and verify_otp.php with new 'bep20_update' type</li>
        <li><strong>Frontend Modal:</strong> JavaScript modal appears when user tries to update existing BEP20 address</li>
        <li><strong>Security Features:</strong> OTP expires in 10 minutes, email verification required for address changes</li>
    </ul>
    
    <h3>How it works:</h3>
    <ol>
        <li>User enters BEP20 address in profile form</li>
        <li>If it's the first time setting the address, it validates format and saves directly</li>
        <li>If user already has a BEP20 address, system shows OTP modal</li>
        <li>User clicks "Send OTP" to receive email with verification code</li>
        <li>User enters OTP and clicks "Verify & Update"</li>
        <li>System verifies OTP and updates the address if valid</li>
    </ol>
    
    <h3>Files modified:</h3>
    <ul>
        <li><strong>profile.php:</strong> Added JavaScript for BEP20 validation and OTP modal</li>
        <li><strong>profile_model.php:</strong> Added BEP20 validation and OTP verification logic</li>
        <li><strong>send_otp.php:</strong> Added 'bep20_update' email template</li>
        <li><strong>validate_bep20.php:</strong> New file with validation functions</li>
    </ul>
</div>

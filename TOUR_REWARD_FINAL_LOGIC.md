# Tour Reward - Final Implementation Logic

## ✅ Complete Implementation in `cron_tb.php` Only

### 🎯 **Tour Reward Requirements:**
- **Total Tour Business**: $10,000
- **Calculation**: 50% from strongest direct + 50% from all other directs
- **Processing**: Daily in `cron_tb.php`
- **Storage**: Only final reward in `income_reward` table (type=2)

## 🔄 **Step-by-Step Process:**

### 1. **Get All Direct Users**
```php
$direct_users_query = "SELECT uid FROM user WHERE refer_id = '".$uid."' AND status = 0 AND topup > 0";
```

### 2. **Calculate Each Direct User's Team Business**
For each direct user, calculate:
- **Own Business**: `get_sum('investments', 'amount', "uid = '".$direct_uid."' AND ipid <= 8")`
- **Downline Business**: Recursive calculation of all their referrals
- **Total Team Business**: Own Business + Downline Business

### 3. **Sort Direct Users by Team Business**
```php
usort($direct_users, function($a, $b) {
    return $b['teamb'] - $a['teamb'];
});
```

### 4. **Calculate Tour Business**
- **Strongest Direct**: 50% from the highest team business direct user
- **Other Directs**: 50% from sum of all other direct users' team business
- **Tour Business**: Strongest 50% + Others 50%

### 5. **Check Eligibility & Mark as Eligible**
```php
if($tour_business >= 10000) {
    // Check if already marked as eligible
    $check_tour_reward = my_num_rows(my_query("SELECT recid FROM income_reward WHERE uid='".$uid."' AND reward=99 AND type=2"));

    if(!$check_tour_reward) {
        // Mark as eligible for Tour Reward (no monetary reward)
        my_query("INSERT INTO income_reward (uid, reward, datetime, type, status) VALUES ('".$uid."', 99, '".date('Y-m-d H:i:s')."', 2, 1)");
    }
}
```

## 📊 **Example Calculation:**

### User has these direct referrals:
1. **Direct User A**: 
   - Own investments: $5,000
   - Downline business: $10,000
   - **Total Team Business**: $15,000

2. **Direct User B**:
   - Own investments: $3,000
   - Downline business: $5,000
   - **Total Team Business**: $8,000

3. **Direct User C**:
   - Own investments: $2,000
   - Downline business: $3,000
   - **Total Team Business**: $5,000

### Tour Business Calculation:
- **Strongest Direct**: $15,000 (User A)
- **Other Directs Sum**: $8,000 + $5,000 = $13,000 (User B + C)
- **Tour Business**: ($15,000 × 0.5) + ($13,000 × 0.5) = $7,500 + $6,500 = $14,000

### Result: ✅ **Eligible for Tour** ($14,000 ≥ $10,000) - No monetary reward, just eligibility tracking

## 🗂️ **Functions Added:**

### `calculate_downline_business($uid)`
Recursively calculates total business of all downline users:
```php
function calculate_downline_business($uid) {
    $total_downline_business = 0;
    
    // Get all direct children
    $children_query = "SELECT uid FROM user WHERE refer_id = '$uid' AND status = 0 AND topup > 0";
    $children_result = my_query($children_query);
    
    while($child = my_fetch_object($children_result)) {
        $child_uid = $child->uid;
        
        // Child's own business
        $child_business = get_sum('investments', 'amount', "uid = '".$child_uid."' AND ipid <= 8");
        $total_downline_business += $child_business;
        
        // Child's downline business (recursive)
        $total_downline_business += calculate_downline_business($child_uid);
    }
    
    return $total_downline_business;
}
```

## 🗄️ **Database Storage:**

### `income_reward` Table Only:
- `uid`: User ID who is eligible for tour
- `reward`: 99 (Tour Eligibility identifier)
- `datetime`: When eligibility was recorded
- `type`: 2 (Special eligibility type)
- `status`: 1 (Active/Completed)

**Note**: No monetary amount - just eligibility tracking

### No Intermediate Storage:
- ❌ No fields added to user table
- ❌ No temporary calculations stored
- ✅ Direct calculation and reward in one process

## 🔍 **Database Queries:**

### Check Tour Eligible Users:
```sql
SELECT ir.*, u.login_id
FROM income_reward ir
JOIN user u ON ir.uid = u.uid
WHERE ir.reward = 99 AND ir.type = 2
ORDER BY ir.datetime DESC;
```

### Count Tour Eligible Users:
```sql
SELECT COUNT(*) as total_tour_eligible
FROM income_reward
WHERE reward = 99 AND type = 2;
```

## ⏰ **Cron Schedule:**

```bash
# Daily team business calculation and Tour Eligibility processing
0 2 * * * /usr/bin/php /path/to/cron_tb.php
```

## 📈 **Expected Output:**

```
Processing user UID: 123
Direct User 101: Team Business = $15,000 (Own: $5,000 + Downline: $10,000)
Direct User 102: Team Business = $8,000 (Own: $3,000 + Downline: $5,000)
Direct User 103: Team Business = $5,000 (Own: $2,000 + Downline: $3,000)

Tour Business Calculation:
- Strongest Direct (50%): $15,000 × 0.5 = $7,500
- Other Directs (50%): ($8,000 + $5,000) × 0.5 = $6,500
- Total Tour Business: $14,000

✅ Tour Reward (Europe Tour) ELIGIBILITY recorded for user UID: 123 - Tour Business: $14,000.00 (Strongest: $15,000.00, Others: $13,000.00)
```

## 🎉 **Benefits:**

✅ **Real-time calculation** - No stale data
✅ **Accurate team business** - Includes full downline
✅ **No database bloat** - Only stores eligibility records
✅ **Duplicate prevention** - One eligibility record per user
✅ **Daily processing** - Immediate eligibility check
✅ **Clean implementation** - All logic in one place
✅ **No monetary rewards** - Pure eligibility tracking

## 🚀 **Ready to Use!**

The system now:
1. **Calculates proper team business** for each direct user
2. **Applies 50/50 logic** correctly
3. **Marks eligibility immediately** when qualified
4. **Stores only eligibility records** in income_reward table
5. **Runs daily** for real-time processing

No additional database changes needed - uses existing tables and functions!

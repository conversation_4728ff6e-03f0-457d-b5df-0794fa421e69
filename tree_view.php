<?php
session_start();
include_once '../lib/config.php';

// Handle AJAX request for downline data
if (isset($_POST['action']) && $_POST['action'] == 'get_downline') {
    $requested_uid = tres($_POST['user_id']);
    $downline_data = getDownlineData($requested_uid);
    echo json_encode($downline_data);
    exit;
}

// Check if user is logged in
if (!isset($_SESSION['userid']) || empty($_SESSION['userid'])) {
    header('Location: index.php');
    exit;
}

$uid = $_SESSION['userid'];
$user = get_user_details($uid);

// Debug: Check if user data is retrieved
if (!$user) {
    echo "Error: User data not found for UID: " . $uid;
    exit;
}

$reward_arr = get_reward();
$title = "Tree View";

$childs = get_single_dimensional(get_child_levels($uid, 'yes'));
if (isset($_GET['login_id'])) {
    $login_id = tres($_GET['login_id']);
    $no = registeredUserId($login_id);
    if ($no != 0) {
        $uid = $no;
    }
}
if (isset($_GET['no'])) {
    $no = tres($_GET['no']);
    if ($no != 0) {
        $uid = $no;
    }
}

if (!in_array($uid, $childs)) {
    redirect('tree_view.php');
}

$userF = get_user_details($uid);
$rsS = my_query("SELECT * FROM user WHERE placement_id = '$uid'");
?>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<title><?php echo $title; ?> - <?php echo SITE_NAME; ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap/css/bootstrap.min.css?vs=100">
<link rel="icon" type="image/png" href="./assets/fav.png" sizes="96x96" />
<link rel="stylesheet" href="./assets/cy/libs/priority-navigation-master/dist/priority-nav-core.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/swiper/swiper.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/bootstrap-select-1.14.0/dist/css/bootstrap-select.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/plyr/dist/plyr.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/ion.rangeSlider-master/css/ion.rangeSlider.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/libs/toastr-master/build/toastr.min.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/main.css?vs=100">
<link rel="stylesheet" href="./assets/cy/css/media.css?vs=100">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="shortcut icon" href="./assets/fav.png" />

<style>
.openUserInfo {
	cursor: pointer;
}
.sectionLotteriesItemsSMEmpty {
	min-height: 46px;
}
button.change-pswd-type-link {
	border: 0px;
}
.logo-img img {
	width: 150px;
}
.db-page--wide-menu .db-side-logo__text-block {
  width: 200px !important;
}.db-side-logo__text {
  padding-left: 0 !important;
}
.mobile-panel-top-left .logo-wrapper {
  display: flex;
  max-width: 150px;
}
.db-side-logo__text .image {
  width: 150px;
  min-width: 61px;
  max-width: none;
}
.db-page-topline-panel__right__logo .logo-wrapper {
  max-width: 160px;
}
.topline-refill-btn {
  line-height: 1;
  color: #EBF4FF;
  font-size: 15px;
  text-align: center;
  height: 40px;
}
.topline-refill-btn {
  width: 170px;
}
.toplinen img {
  width: 17px;
}
@media only screen and (min-width: 601px) {
  .db-page-topline__right {
  margin-left: -200px;
}
}

.dropdown-container {
  margin-top: 5px;
  border-radius: 5px;
  padding-bottom: 5px;
}
.dropdown-container {
  display: none;
  border-radius:5px;
}

@media screen and (max-width: 780px) {
 .mobhidden{display:none !important;}
  .cabMenu li.hidden {
    display: flex !important;
  }
  .langList{display:none}
}

.dropdown-btn .fa {
  position: absolute;
  right: 5px;
  top: 65px;
  font-size: 20px;
  color: #fff;
}
.fa-caret-up{display:none;}
.dactive .fa-caret-down{display:none;}
.dactive .fa-caret-up{display:inline-block !important;}
.dropdown-container li {
  height: auto;
  padding: 5px 10px 5px 10px;
  line-height: 1;
}
.dropdown-containers li:hover{
 background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);;
 color:#fff;
}
.dropdown-container li:hover cap{
 color:#fff;
}
.hidden:hover {
  background: linear-gradient(114deg, #17c964 0%, #0170ef 100%);
}

.capa a {
	margin-left:20px;  color: #fff;
  text-decoration: none;
  font-size:14px;
}
.db-sidemenu-icon img {
  width: 25px;
}
.db-sidemenu-icon {
  position: inherit;
}
.mobile-db-menu-link__text {
  margin-left: 5px;
}

/* Enhanced Tree View Styling */
.tree-container {
    background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 50%, #0f1419 100%);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 30px;
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.05);
}

.tree-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(23, 201, 100, 0.1) 0%,
        rgba(1, 112, 239, 0.1) 25%,
        transparent 50%,
        rgba(23, 201, 100, 0.05) 75%,
        rgba(1, 112, 239, 0.05) 100%);
    border-radius: 20px;
    z-index: -1;
}

.tree-container::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #17c964, #0170ef, #17c964);
    border-radius: 22px;
    z-index: -2;
    opacity: 0.3;
}

.tree-search-form {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 35px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.tree-search-form .form-control {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%);
    border: 2px solid rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border-radius: 12px;
    padding: 15px 20px;
    font-size: 16px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.tree-search-form .form-control:focus {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.12) 100%);
    border-color: rgba(23, 201, 100, 0.6);
    box-shadow: 0 0 0 0.3rem rgba(23, 201, 100, 0.15),
                0 8px 25px rgba(23, 201, 100, 0.2);
    color: #fff;
    transform: translateY(-2px);
}

.tree-search-form .form-control::placeholder {
    color: #a0a9b8;
    font-weight: 400;
}

.tree-search-btn {
    background: linear-gradient(135deg, #17c964 0%, #0170ef 100%);
    border: none;
    color: #fff;
    padding: 15px 25px;
    border-radius: 12px;
    font-weight: 700;
    font-size: 16px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tree-search-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.tree-search-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 35px rgba(23, 201, 100, 0.4),
                0 5px 15px rgba(1, 112, 239, 0.3);
    color: #fff;
}

.tree-search-btn:hover::before {
    left: 100%;
}

.tree-search-btn:active {
    transform: translateY(-1px) scale(0.98);
}

.tree-view-area {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(15, 20, 25, 0.4) 100%);
    border-radius: 20px;
    padding: 40px;
    min-height: 500px;
    max-height: 85vh; /* Added max height for better viewport management */
    overflow-y: auto; /* Enable smooth scrolling */
    overflow-x: hidden;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(10px);
    /* Enhanced scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: rgba(23, 201, 100, 0.6) rgba(255, 255, 255, 0.1);
}

.tree-node {
    position: relative;
    display: inline-block;
    margin: 20px;
    text-align: center;
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.tree-user {
    background: linear-gradient(135deg, #1a1f2e 0%, #0f1419 50%, #1a1f2e 100%);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 18px;
    padding: 20px;
    min-width: 140px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    cursor: pointer;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.tree-user::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    border-radius: 20px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tree-user:hover {
    transform: translateY(-8px) scale(1.05);
    border-color: rgba(23, 201, 100, 0.6);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4),
                0 0 30px rgba(23, 201, 100, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.tree-user:hover::before {
    opacity: 1;
}

.tree-user.active {
    border-color: #17c964;
    background: linear-gradient(135deg, rgba(23, 201, 100, 0.15) 0%, rgba(1, 112, 239, 0.15) 50%, rgba(23, 201, 100, 0.1) 100%);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(23, 201, 100, 0.2),
                inset 0 1px 0 rgba(23, 201, 100, 0.3);
}

.tree-user.inactive {
    border-color: #f6465d;
    background: linear-gradient(135deg, rgba(246, 70, 93, 0.15) 0%, rgba(246, 70, 93, 0.1) 50%, rgba(246, 70, 93, 0.05) 100%);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(246, 70, 93, 0.2),
                inset 0 1px 0 rgba(246, 70, 93, 0.3);
}

.tree-user.blocked {
    border-color: #848e9c;
    background: linear-gradient(135deg, rgba(132, 142, 156, 0.15) 0%, rgba(132, 142, 156, 0.1) 50%, rgba(132, 142, 156, 0.05) 100%);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(132, 142, 156, 0.2),
                inset 0 1px 0 rgba(132, 142, 156, 0.3);
}

.tree-user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #17c964 0%, #0170ef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    font-size: 22px;
    font-weight: 800;
    color: #fff;
    position: relative;
    box-shadow: 0 4px 15px rgba(23, 201, 100, 0.3),
                inset 0 2px 4px rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.tree-user-avatar::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, #17c964, #0170ef, #17c964);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tree-user:hover .tree-user-avatar::before {
    opacity: 0.7;
}

.tree-user.inactive .tree-user-avatar {
    background: linear-gradient(135deg, #f6465d 0%, #ff6b7a 100%);
    box-shadow: 0 4px 15px rgba(246, 70, 93, 0.3),
                inset 0 2px 4px rgba(255, 255, 255, 0.2);
}

.tree-user.inactive .tree-user-avatar::before {
    background: linear-gradient(45deg, #f6465d, #ff6b7a, #f6465d);
}

.tree-user.blocked .tree-user-avatar {
    background: linear-gradient(135deg, #848e9c 0%, #9ca3af 100%);
    box-shadow: 0 4px 15px rgba(132, 142, 156, 0.3),
                inset 0 2px 4px rgba(255, 255, 255, 0.2);
}

.tree-user.blocked .tree-user-avatar::before {
    background: linear-gradient(45deg, #848e9c, #9ca3af, #848e9c);
}

.tree-user-id {
    font-size: 11px;
    color: #a0a9b8;
    margin-bottom: 6px;
    font-weight: 500;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.tree-user-name {
    font-size: 15px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 10px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.tree-user-status {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.tree-user-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.tree-user:hover .tree-user-status::before {
    left: 100%;
}

.tree-user-status.active {
    background: linear-gradient(135deg, rgba(23, 201, 100, 0.3) 0%, rgba(23, 201, 100, 0.2) 100%);
    color: #17c964;
    border: 1px solid rgba(23, 201, 100, 0.4);
    box-shadow: 0 2px 8px rgba(23, 201, 100, 0.2);
}

.tree-user-status.inactive {
    background: linear-gradient(135deg, rgba(246, 70, 93, 0.3) 0%, rgba(246, 70, 93, 0.2) 100%);
    color: #f6465d;
    border: 1px solid rgba(246, 70, 93, 0.4);
    box-shadow: 0 2px 8px rgba(246, 70, 93, 0.2);
}

.tree-user-status.blocked {
    background: linear-gradient(135deg, rgba(132, 142, 156, 0.3) 0%, rgba(132, 142, 156, 0.2) 100%);
    color: #848e9c;
    border: 1px solid rgba(132, 142, 156, 0.4);
    box-shadow: 0 2px 8px rgba(132, 142, 156, 0.2);
}

.tree-new-user {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border: 3px dashed rgba(255, 255, 255, 0.3);
    border-radius: 18px;
    padding: 25px;
    min-width: 140px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
    text-decoration: none;
    color: #a0a9b8;
    position: relative;
    backdrop-filter: blur(5px);
    overflow: hidden;
}

.tree-new-user::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(23, 201, 100, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.4s ease;
    z-index: -1;
}

.tree-new-user:hover {
    border-color: rgba(23, 201, 100, 0.7);
    background: linear-gradient(135deg, rgba(23, 201, 100, 0.1) 0%, rgba(1, 112, 239, 0.1) 100%);
    color: #17c964;
    text-decoration: none;
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3),
                0 0 25px rgba(23, 201, 100, 0.2);
}

.tree-new-user:hover::before {
    width: 200px;
    height: 200px;
}

.tree-new-user i {
    font-size: 28px;
    margin-bottom: 10px;
    display: block;
    transition: all 0.3s ease;
}

.tree-new-user:hover i {
    transform: scale(1.2) rotate(90deg);
    color: #17c964;
}

.tree-connection-line {
    position: absolute;
    background: linear-gradient(90deg, #17c964 0%, #0170ef 50%, #17c964 100%);
    height: 3px;
    z-index: 1;
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(23, 201, 100, 0.3);
}

.tree-connection-vertical {
    position: absolute;
    background: linear-gradient(180deg, #17c964 0%, #0170ef 50%, #17c964 100%);
    width: 3px;
    z-index: 1;
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(23, 201, 100, 0.3);
}

/* Enhanced Tree Tooltip with Smart Positioning */
.tree-tooltip {
    position: fixed;
    background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 50%, #0f1419 100%);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    padding: 20px;
    min-width: 300px;
    max-width: 350px;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.8),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    transform: translateY(20px) scale(0.9);
    pointer-events: none;
}

.tree-user:hover .tree-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateY(-10px) scale(1);
    pointer-events: auto;
}

.tree-tooltip::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #17c964, #0170ef, #17c964);
    border-radius: 18px;
    z-index: -1;
    opacity: 0.3;
}

.tree-tooltip h4 {
    color: #fff;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 700;
    background: linear-gradient(135deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.tree-tooltip-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 15px;
}

.tree-tooltip-stat {
    text-align: center;
    padding: 12px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tree-tooltip-stat::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(23, 201, 100, 0.1), transparent);
    transition: left 0.5s;
}

.tree-tooltip-stat:hover::before {
    left: 100%;
}

.tree-tooltip-stat:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.tree-tooltip-stat-label {
    font-size: 10px;
    color: #a0a9b8;
    text-transform: uppercase;
    margin-bottom: 5px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.tree-tooltip-stat-value {
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .tree-container {
        padding: 25px 15px;
        margin-bottom: 20px;
    }

    .tree-search-form {
        padding: 20px 15px;
        margin-bottom: 25px;
    }

    .tree-search-form .form-control {
        padding: 12px 15px;
        font-size: 14px;
    }

    .tree-search-btn {
        padding: 12px 20px;
        font-size: 14px;
    }

    .tree-view-area {
        padding: 25px 15px;
        min-height: 400px;
    }

    .tree-node {
        margin: 15px 10px;
    }

    .tree-user {
        min-width: 120px;
        padding: 15px;
    }

    .tree-user-avatar {
        width: 50px;
        height: 50px;
        font-size: 18px;
    }

    .tree-user-name {
        font-size: 13px;
    }

    .tree-user-id {
        font-size: 10px;
    }

    .tree-user-status {
        padding: 4px 8px;
        font-size: 9px;
    }

    .tree-tooltip {
        min-width: 240px;
        padding: 15px;
        left: 50% !important;
        transform: translateX(-50%) translateY(-15px) scale(1) !important;
    }

    .tree-tooltip h4 {
        font-size: 16px;
    }

    .tree-tooltip-stats {
        gap: 8px;
    }

    .tree-tooltip-stat {
        padding: 8px;
    }

    .tree-tooltip-stat-value {
        font-size: 14px;
    }

    .tree-new-user {
        min-width: 120px;
        padding: 20px 15px;
    }

    .tree-new-user i {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .tree-container {
        padding: 20px 10px;
        border-radius: 15px;
    }

    .tree-search-form {
        padding: 15px;
        border-radius: 12px;
    }

    .tree-view-area {
        padding: 20px 10px;
        border-radius: 15px;
    }

    .tree-user {
        min-width: 100px;
        padding: 12px;
        border-radius: 12px;
    }

    .tree-user-avatar {
        width: 45px;
        height: 45px;
        font-size: 16px;
    }

    .tree-tooltip {
        min-width: 200px;
        padding: 12px;
        border-radius: 12px;
    }

    .tree-tooltip-stats {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .tree-new-user {
        min-width: 100px;
        padding: 15px 10px;
        border-radius: 12px;
    }
}

/* Additional animations and effects */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(23, 201, 100, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(23, 201, 100, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(23, 201, 100, 0);
    }
}

.tree-user.active:hover {
    animation: pulse 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.tree-search-btn:hover {
    background-image: linear-gradient(135deg, #17c964 0%, #0170ef 100%),
                      linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    background-size: 100% 100%, 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Loading and transition effects */
.tree-view-area {
    animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced focus states */
.tree-search-form .form-control:focus {
    outline: none;
}

/* Improved scrollbar for webkit browsers */
.tree-view-area::-webkit-scrollbar {
    width: 8px;
}

.tree-view-area::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

.tree-view-area::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #17c964, #0170ef);
    border-radius: 4px;
}

.tree-view-area::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #0170ef, #17c964);
}

/* Enhanced page title styling */
.refill-form-title {
    position: relative;
    display: inline-block;
}

.refill-form-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #17c964, #0170ef);
    border-radius: 2px;
    transform: translateX(-50%);
}

/* Improved container styling */
.tree-container {
    position: relative;
    animation: containerFadeIn 1s ease-out;
}

@keyframes containerFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ripple effect for tree user clicks */
.tree-user {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(23, 201, 100, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.tree-user.clicked {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

/* Enhanced form focus states */
.tree-search-form.form-focused {
    border-color: rgba(23, 201, 100, 0.4);
    box-shadow: 0 0 0 0.2rem rgba(23, 201, 100, 0.15);
}

.input-focused .form-control {
    border-color: rgba(23, 201, 100, 0.6) !important;
}

/* Tooltip show animation */
.tooltip-show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(-15px) scale(1) !important;
}

/* Enhanced button states */
.tree-search-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
}

/* Improved tree node spacing */
.tree-node {
    margin: 25px 15px;
}

/* Enhanced connection lines with animation */
.tree-connection-line,
.tree-connection-vertical {
    animation: lineGlow 2s ease-in-out infinite alternate;
}

@keyframes lineGlow {
    from {
        box-shadow: 0 0 5px rgba(23, 201, 100, 0.3);
    }
    to {
        box-shadow: 0 0 15px rgba(23, 201, 100, 0.6);
    }
}

/* Level-based Tree UI Styles */
.level-selector-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

/* Enhanced level container transitions */
.level-container {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: translateY(20px);
    opacity: 0;
}

.level-container.active {
    transform: translateY(0);
    opacity: 1;
}

.level-selector, .status-filter {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%);
    border: 2px solid rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border-radius: 12px;
    padding: 12px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.level-selector:focus, .status-filter:focus {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.18) 0%, rgba(255, 255, 255, 0.12) 100%);
    border-color: rgba(23, 201, 100, 0.6);
    box-shadow: 0 0 0 0.3rem rgba(23, 201, 100, 0.15);
    color: #fff;
    outline: none;
}

.level-container {
    margin-bottom: 40px;
    display: none;
}

.level-container.active {
    display: block;
    animation: fadeInUp 0.6s ease-out;
}

.level-header {
    text-align: center;
    margin-bottom: 30px;
}

.level-header h3 {
    color: #ffffff;
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(135deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    padding: 20px 0;
}

.tree-user-card {
    background: linear-gradient(135deg, #1a1f2e 0%, #0f1419 50%, #1a1f2e 100%);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 18px;
    padding: 25px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
    user-select: none;
}

.tree-user-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tree-user-card:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(23, 201, 100, 0.6);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4),
                0 0 30px rgba(23, 201, 100, 0.3);
}

.tree-user-card:hover::before {
    opacity: 1;
}

.tree-user-card.active {
    border-color: #17c964;
    background: linear-gradient(135deg, rgba(23, 201, 100, 0.15) 0%, rgba(1, 112, 239, 0.15) 50%, rgba(23, 201, 100, 0.1) 100%);
}

.tree-user-card.inactive {
    border-color: #f6465d;
    background: linear-gradient(135deg, rgba(246, 70, 93, 0.15) 0%, rgba(246, 70, 93, 0.1) 50%, rgba(246, 70, 93, 0.05) 100%);
}

.tree-user-card.blocked {
    border-color: #848e9c;
    background: linear-gradient(135deg, rgba(132, 142, 156, 0.15) 0%, rgba(132, 142, 156, 0.1) 50%, rgba(132, 142, 156, 0.05) 100%);
}

.tree-user-info {
    text-align: center;
    margin-bottom: 20px;
}

.tree-user-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.btn-view-details, .btn-navigate {
    background: linear-gradient(135deg, #17c964 0%, #0170ef 100%);
    border: none;
    color: #fff;
    padding: 10px 15px;
    border-radius: 10px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-view-details:hover, .btn-navigate:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(23, 201, 100, 0.4);
}

/* User Details Modal */
.user-details-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(10px);
}

.user-details-modal.show {
    display: flex;
    animation: modalFadeIn 0.3s ease-out;
}

.modal-content {
    background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 50%, #0f1419 100%);
    border-radius: 20px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border: 2px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.8);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h4 {
    color: #ffffff;
    margin: 0;
    background: linear-gradient(135deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.modal-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(246, 70, 93, 0.2);
    color: #f6465d;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Copy notification styles */
.copy-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #17c964 0%, #0170ef 100%);
    color: white;
    padding: 15px 25px;
    border-radius: 12px;
    font-weight: 600;
    z-index: 10001;
    transform: translateX(400px);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 10px 30px rgba(23, 201, 100, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.copy-notification.show {
    transform: translateX(0);
}

.copy-notification i {
    margin-right: 10px;
    font-size: 16px;
}

/* Card click effect */
.tree-user-card.copying {
    transform: scale(0.95);
    border-color: #17c964;
    box-shadow: 0 0 30px rgba(23, 201, 100, 0.5);
}

/* User ID highlight effect */
.tree-user-id.copied {
    color: #17c964 !important;
    font-weight: 700;
    animation: idCopied 0.6s ease-out;
}

@keyframes idCopied {
    0% {
        transform: scale(1);
        color: #a0a9b8;
    }
    50% {
        transform: scale(1.1);
        color: #17c964;
    }
    100% {
        transform: scale(1);
        color: #17c964;
    }
}

/* Copy indicator styles */
.copy-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(23, 201, 100, 0.1);
    border: 1px solid rgba(23, 201, 100, 0.3);
    border-radius: 8px;
    padding: 5px 8px;
    font-size: 10px;
    color: #17c964;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    pointer-events: none;
    backdrop-filter: blur(5px);
}

.copy-indicator i {
    margin-right: 4px;
    font-size: 9px;
}

.tree-user-card:hover .copy-indicator {
    opacity: 1;
    transform: translateY(0);
}

.tree-user-card.copying .copy-indicator {
    background: rgba(23, 201, 100, 0.2);
    border-color: rgba(23, 201, 100, 0.6);
    opacity: 1;
    transform: translateY(0) scale(1.1);
}

/* Navigation Info Styles */
.tree-navigation-info {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.navigation-card {
    text-align: center;
    padding: 20px;
}

.navigation-icon {
    font-size: 48px;
    color: #17c964;
    margin-bottom: 20px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.navigation-card h4 {
    color: #ffffff;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navigation-card p {
    color: #a0a9b8;
    font-size: 16px;
    margin-bottom: 20px;
    line-height: 1.6;
}

.navigation-tips {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.tip {
    background: rgba(23, 201, 100, 0.1);
    border: 1px solid rgba(23, 201, 100, 0.3);
    border-radius: 20px;
    padding: 8px 15px;
    font-size: 12px;
    color: #17c964;
    font-weight: 600;
}

/* Expand Indicator Styles */
.expand-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(1, 112, 239, 0.1);
    border: 1px solid rgba(1, 112, 239, 0.3);
    border-radius: 8px;
    padding: 5px 8px;
    font-size: 10px;
    color: #0170ef;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    pointer-events: none;
    backdrop-filter: blur(5px);
}

.expand-indicator i {
    margin-right: 4px;
    font-size: 9px;
    transition: transform 0.3s ease;
}

.tree-user-card:hover .expand-indicator {
    opacity: 1;
    transform: translateY(0);
}

.tree-user-card.expanded .expand-indicator i {
    transform: rotate(180deg);
}

.tree-user-card.expanded .expand-indicator {
    background: rgba(1, 112, 239, 0.2);
    border-color: rgba(1, 112, 239, 0.6);
    opacity: 1;
    transform: translateY(0);
}

/* Expandable Tree Container */
.expandable-tree-container {
    max-height: 0;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(15, 20, 25, 0.3) 100%);
    border-radius: 0 0 18px 18px;
    margin-top: -18px;
    padding: 0 25px;
    border: 2px solid transparent;
    border-top: none;
}

.expandable-tree-container.expanded {
    max-height: 80vh; /* Changed from fixed 1000px to viewport-based height */
    overflow-y: auto; /* Added smooth scrolling */
    overflow-x: hidden;
    padding: 25px;
    border-color: rgba(255, 255, 255, 0.1);
    margin-top: 0;
    /* Enhanced scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: rgba(23, 201, 100, 0.6) rgba(255, 255, 255, 0.1);
}

/* Enhanced scrollbar for expandable containers */
.expandable-tree-container.expanded::-webkit-scrollbar {
    width: 8px;
}

.expandable-tree-container.expanded::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

.expandable-tree-container.expanded::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #17c964, #0170ef);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.expandable-tree-container.expanded::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #0170ef, #17c964);
}

.tree-loading {
    text-align: center;
    padding: 30px;
    color: #a0a9b8;
    display: none;
}

.tree-loading.show {
    display: block;
}

.tree-loading i {
    font-size: 24px;
    margin-bottom: 10px;
    color: #17c964;
}

.tree-content {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.4s ease;
}

.tree-content.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Tree User Stats */
.tree-user-stats {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
    display: inline-flex;
    align-items: center;
    font-size: 11px;
    color: #a0a9b8;
    font-weight: 600;
}

.stat-item i {
    margin-right: 5px;
    color: #17c964;
}

/* Enhanced Action Buttons */
.btn-copy-id {
    background: linear-gradient(135deg, #0170ef 0%, #17c964 100%);
    border: none;
    color: #fff;
    padding: 10px 15px;
    border-radius: 10px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-copy-id:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(1, 112, 239, 0.4);
}

/* Mini Tree View Styles */
.mini-tree-view {
    padding: 20px 0;
}

.mini-tree-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mini-tree-header h5 {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    background: linear-gradient(135deg, #17c964 0%, #0170ef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.mini-tree-grid {
    display: grid;
    gap: 15px;
}

.mini-tree-message {
    text-align: center;
    padding: 30px 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.mini-tree-message i {
    font-size: 32px;
    color: #17c964;
    margin-bottom: 15px;
    display: block;
}

.mini-tree-message p {
    color: #a0a9b8;
    margin-bottom: 20px;
    font-size: 14px;
    line-height: 1.5;
}

.btn-load-full-tree {
    background: linear-gradient(135deg, #17c964 0%, #0170ef 100%);
    border: none;
    color: #fff;
    padding: 12px 20px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

/* Downline Tree Styles */
.downline-level {
    margin-bottom: 25px;
}

.level-title {
    display: flex;
    align-items: center;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    padding: 10px 15px;
    background: linear-gradient(135deg, rgba(23, 201, 100, 0.1) 0%, rgba(1, 112, 239, 0.1) 100%);
    border-radius: 8px;
    border-left: 4px solid #17c964;
}

.level-title i {
    margin-right: 8px;
    color: #17c964;
}

.level-users {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.mini-user-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
}

.mini-user-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.mini-user-card:hover {
    transform: translateX(5px);
    border-color: rgba(23, 201, 100, 0.5);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.mini-user-card:hover::before {
    left: 100%;
}

.mini-user-card.active {
    border-color: #17c964;
    background: linear-gradient(135deg, rgba(23, 201, 100, 0.15) 0%, rgba(23, 201, 100, 0.05) 100%);
}

.mini-user-card.inactive {
    border-color: #f6465d;
    background: linear-gradient(135deg, rgba(246, 70, 93, 0.15) 0%, rgba(246, 70, 93, 0.05) 100%);
}

.mini-user-card.blocked {
    border-color: #848e9c;
    background: linear-gradient(135deg, rgba(132, 142, 156, 0.15) 0%, rgba(132, 142, 156, 0.05) 100%);
}

.mini-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #17c964 0%, #0170ef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 700;
    color: #fff;
    flex-shrink: 0;
}

.mini-user-card.inactive .mini-user-avatar {
    background: linear-gradient(135deg, #f6465d 0%, #ff6b7a 100%);
}

.mini-user-card.blocked .mini-user-avatar {
    background: linear-gradient(135deg, #848e9c 0%, #9ca3af 100%);
}

.mini-user-info {
    flex: 1;
}

.mini-user-id {
    font-size: 11px;
    color: #a0a9b8;
    font-weight: 600;
    margin-bottom: 2px;
}

.mini-user-name {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 4px;
}

.mini-user-status {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 9px;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 6px;
}

.mini-user-status.active {
    background: rgba(23, 201, 100, 0.2);
    color: #17c964;
}

.mini-user-status.inactive {
    background: rgba(246, 70, 93, 0.2);
    color: #f6465d;
}

.mini-user-status.blocked {
    background: rgba(132, 142, 156, 0.2);
    color: #848e9c;
}

.mini-user-meta {
    font-size: 10px;
    color: #848e9c;
}

.mini-user-meta i {
    margin-right: 3px;
    color: #17c964;
}

.mini-user-arrow {
    color: #a0a9b8;
    font-size: 12px;
    transition: all 0.3s ease;
}

.mini-user-card:hover .mini-user-arrow {
    color: #17c964;
    transform: translateX(3px);
}

.empty-tree {
    text-align: center;
    padding: 40px 20px;
    color: #848e9c;
}

.empty-tree i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-tree h6 {
    color: #a0a9b8;
    font-size: 16px;
    margin-bottom: 8px;
}

.empty-tree p {
    font-size: 14px;
    margin: 0;
}

.loading-placeholder {
    text-align: center;
    padding: 30px;
    color: #a0a9b8;
}

.loading-placeholder i {
    font-size: 24px;
    color: #17c964;
    margin-bottom: 10px;
}

/* Enhanced responsive design for expandable trees */
@media (max-width: 768px) {
    .navigation-tips {
        flex-direction: column;
        align-items: center;
    }

    .tip {
        margin-bottom: 8px;
    }

    .expandable-tree-container.expanded {
        padding: 15px;
        max-height: 70vh; /* Reduced height for mobile */
    }

    .tree-view-area {
        max-height: 75vh; /* Adjusted for mobile */
        padding: 25px 15px;
    }

    .mini-tree-message {
        padding: 20px 15px;
    }

    .expand-indicator,
    .copy-indicator {
        font-size: 9px;
        padding: 4px 6px;
    }

    .tree-user-stats {
        margin-top: 8px;
        padding-top: 8px;
    }

    /* Improved mobile scrolling */
    .expandable-tree-container.expanded,
    .tree-view-area {
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    }
}
</style>

</head>

<body class="page page--main">
<div class="page-inner">
<div class="page-progress-block xProgress d-none">
  <div class="page-progress">
    <div class="page-progress__progress xProgressPercent" style="width: 0%;"></div>
  </div>
</div>

<div class="mobile-panel-block">
  <div class="mobile-panel-top-block">
    <div class="mobile-panel-top-left">
      <div class="logo-wrapper"> <a href="" class="logo">
        <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
        </a> </div>
    </div>
    <div class="mobile-panel-top-right">
      <div class="topline-lang-panel-block">
        <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown" aria-expanded="false">
          <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
          <div class="current-lang__text">EN</div>
          </a>
          <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
            <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
            <div class="lang-link__text">EN</div>
            </a> </div>
        </div>
      </div>
      <div class="mobile-panel-close-btn-block">
        <button type="button" class="mobile-panel-close-btn" onclick="closeSidebar()"></button>
      </div>
    </div>
  </div>
  <div class="mobile-db-menu-block">
    <div class="mobile-db-menu">
      <ul>
  <li class="mobile-db-menu-item mobile-db-menu-item--dashboard"> <a href='dashboard.php' class="mobile-db-menu-link mobile-db-menu-link--dashboard">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Dashboard</div>
    </div>
    </a> </li>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--refill">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--withdrawal">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits active">
    <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--deposits ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
    <li class="hidden" ><span class="capa"><a href="report_growth.php" class="openMod" data-modal="buy">Non-Working Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_royalty.php?type=1">Lifetime Cash Back Income</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_binary.php" >Matching Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
    <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--affiliate ">
    <div class="mobile-db-menu-link__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <!--<li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>-->
    <!--<li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>-->
    <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
    <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/lotery.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Lotteries <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="#" class="openMod" data-modal="buy">Buy Lottery</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="#">Report</a></span> </li>
  </div>
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="db-sidemenu-icon"><img src="assets/gaming.png"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Gaming <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
    </div>
    </a> </li>
  <div class="dropdown-container">
    <li class="hidden" ><span class="capa"> <a href="#" class="openMod" data-modal="buy">Play Games</a> </span> </li>
    <li class="hidden" ><span class="capa"><a href="#">Report</a></span> </li>
  </div>
  <!--<li class="mobile-db-menu-item mobile-db-menu-item--settings"> <a href='#' class="mobile-db-menu-link mobile-db-menu-link--settings">-->
  <!--  <div class="db-sidemenu-icon"><img src="assets/promo.png"></div>-->
  <!--  <div class="mobile-db-menu-link__text-block">-->
  <!--    <div class="mobile-db-menu-link__text">Promo Stuff</div>-->
  <!--  </div>-->
  <!--  </a> </li>-->
  <!--  <li class="mobile-db-menu-item mobile-db-menu-item--settings"> <a href='#' class="mobile-db-menu-link mobile-db-menu-link--settings">-->
  <!--  <div class="db-sidemenu-icon"><img src="assets/support.png"></div>-->
  <!--  <div class="mobile-db-menu-link__text-block">-->
  <!--    <div class="mobile-db-menu-link__text">Support Ticket</div>-->
  <!--  </div>-->
  <!--  </a> </li>-->
  <li class="mobile-db-menu-item mobile-db-menu-item--settings dropdown-btn"> <a href="logout.php" class="mobile-db-menu-link mobile-db-menu-link--settings ">
    <div class="topline-logout-btn__icon"></div>
    <div class="mobile-db-menu-link__text-block">
      <div class="mobile-db-menu-link__text">Logout</div>
    </div>
    </a> </li>
</ul>
    </div>
  </div>
  <div class="mobile-panel-menu-block">
    <div class="mobile-panel-menu">
      <div class="mobile-menu">

      </div>
    </div>
  </div>
</div>
<div class="db-page-block">
<div class="container">
<div class="row">
<div class="col-12">
<div class="db-page db-page--wide-menu">
<div class="db-page-left">
  <div class="db-side-block">
    <div class="db-side-logo-block"> <a href="" class="db-side-logo">
      <div class="db-side-logo__text-block">
        <div class="db-side-logo__text"> <img class="image" src="assets/logo.png" alt=""> </div>
      </div>
      </a> </div>
    <div class="db-side-toggle-panel-btn-block">
      <button class="db-side-toggle-panel-btn">
      <div class="db-side-toggle-panel-btn__text-block">
        <div class="db-side-toggle-panel-btn__text">Navigation</div>
      </div>
      <div class="db-side-toggle-panel-btn__icon"></div>
      </button>
    </div>
    <div class="db-sidemenu-block">
      <div class="db-sidemenu">
        <ul>
          <li class="db-sidemenu-item db-sidemenu-item--dashboard"> <a href='dashboard.php' class="db-sidemenu-link db-sidemenu-link--dashboard">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Dashboard</div>
            </div>
            </a> </li>
          <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
             <div class="db-sidemenu-link__text">Account <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div> </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="profile.php" class="openMod" data-modal="buy">Profile</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="change_password.php">Change Password</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--refill dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--refill">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Deposit <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="deposit_block.php" class="openMod" data-modal="buy">Add Fund</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_deposit_block.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--withdrawal dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--withdrawal">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Activation <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="invest.php" class="openMod" data-modal="buy">Activate</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_invest.php">Report</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits active">
            <div class="db-sidemenu-icon"><img src="assets/network.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Network <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="direct_referral_left.php" class="openMod" data-modal="buy">Left Direct</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="direct_referral.php">Right Direct</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="downline.php">Total Team</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="tree_view.php">Tree View</a></span> </li>
      </div>

          <li class="db-sidemenu-item db-sidemenu-item--deposits dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--deposits ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Earning <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <!--<li class="hidden" ><span class="capa"> <a href="report_direct.php" class="openMod" data-modal="buy">Non-Working Bonus</a> </span> </li>-->
             <li class="hidden" ><span class="capa"><a href="report_growth.php" class="openMod" data-modal="buy">Non-Working Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_royalty.php?type=1">Lifetime Cash Back Income</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_binary.php" >Matching Bonus</a></span> </li>
    <li class="hidden" ><span class="capa"><a href="report_royalty.php">Reward Bonus</a></span> </li>
             <!--<li class="hidden" ><span class="capa"><a href="report_all.php">All Reports</a></span> </li>-->

      </div>
          <li class="db-sidemenu-item db-sidemenu-item--affiliate dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--affiliate ">
            <div class="db-sidemenu-link__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Wallet <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <!--<li class="hidden" ><span class="capa"><a href="fund_transfer.php?type=1">Fund Transfer</a></span> </li>-->
             <!--<li class="hidden" ><span class="capa"><a href="fund_transfer3.php?type=1">Self Transfer</a></span> </li>-->
             <li class="hidden" ><span class="capa"><a href="report_fund_transfer.php">Transfer Report</a></span> </li>
             <li class="hidden" ><span class="capa"><a href="profile.php">Wallet Details</a></span> </li>
             <!--<li class="hidden" ><span class="capa"><a href="account.php">Account Overview</a></span> </li>-->
         </div>

             <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/withdraw.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Withdrawal <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="withdrawal_block.php?type=10" class="openMod" data-modal="buy">Fund Withdrawal</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="report_withdrawal_block.php">Report</a></span> </li>
      </div>
      <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/lotery.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Lotteries  <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="#" class="openMod" data-modal="buy">Buy Lottery</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="#">Report</a></span> </li>
      </div>
      <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="javascript:void(0)" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="db-sidemenu-icon"><img src="assets/gaming.png"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Gaming   <i class="fas fa-caret-up"></i><i class="fas fa-caret-down"></i></div>
            </div>
            </a> </li>
          <div class="dropdown-container">
             <li class="hidden" ><span class="capa"> <a href="#" class="openMod" data-modal="buy">Play Games</a> </span> </li>
             <li class="hidden" ><span class="capa"><a href="#">Report</a></span> </li>
      </div>
      <!--<li class="db-sidemenu-item db-sidemenu-item--settings"> <a href='#' class="db-sidemenu-link db-sidemenu-link--settings">-->
      <!--      <div class="db-sidemenu-icon"><img src="assets/promo.png"></div>-->
      <!--      <div class="db-sidemenu-link__text-block">-->
      <!--        <div class="db-sidemenu-link__text">Promo Stuff </div>-->
      <!--      </div>-->
      <!--      </a> </li>-->
      <!--      <li class="db-sidemenu-item db-sidemenu-item--settings"> <a href='#' class="db-sidemenu-link db-sidemenu-link--settings">-->
      <!--      <div class="db-sidemenu-icon"><img src="assets/support.png"></div>-->
      <!--      <div class="db-sidemenu-link__text-block">-->
      <!--        <div class="db-sidemenu-link__text">Support Ticket </div>-->
      <!--      </div>-->
      <!--      </a> </li>-->

            <li class="db-sidemenu-item db-sidemenu-item--settings dropdown-btn"> <a href="logout.php" class="db-sidemenu-link db-sidemenu-link--settings ">
            <div class="topline-logout-btn__icon"></div>
            <div class="db-sidemenu-link__text-block">
              <div class="db-sidemenu-link__text">Logout</div>
            </div>
            </a> </li>
        </ul>
      </div>
    </div>
  </div>
</div>
<div class="db-page-right">
<div class="db-page-topline-block">
  <div class="db-page-topline">
    <div class="db-page-topline__left">
      <div class="topmenu-block">
        <div class="topmenu priority-nav priority-nav-has-dropdown 224">

        </div>
      </div>
    </div>
    <div class="db-page-topline__right">
      <div class="db-page-topline-panel-block">
        <div class="db-page-topline-panel">
          <div class="db-page-topline-panel__left">

            <div class="topline-refill-btn-block"> <a href="profile.php" class="topline-refill-btn">
             <div class="toplinen"><img src="assets/ranking.png"></div>
              <div class="topline-refill-btn__text">Rank : <?php echo $reward_arr[$user->reward] ?? 'Starter'; ?></div>
              </a> </div>

            <div class="topline-refill-btn-block"> <a href="deposit_block.php" class="topline-refill-btn">
              <div class="topline-refill-btn__icon"></div>
              <div class="topline-refill-btn__text">Deposit</div>
              </a> </div>
          </div>
          <div class="db-page-topline-panel__right">
            <div class="db-page-topline-panel__right__logo">

              <div class="logo-wrapper"> <a href="" class="logo">
                <div class="logo-img"> <img class="image" src="assets/logo.png" alt=""> </div>
                </a> </div>
            </div>
            <div class="topline-lang-panel-block">
              <div class="topline-lang-panel"> <a href="javascript:void(0)" class="current-lang" role="button" data-bs-toggle="dropdown"
										aria-expanded="false">
                <div class="current-lang__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                <div class="current-lang__text">EN</div>
                </a>
                <div class="topline-lang-dropdown dropdown-menu dropdown-menu-end"> <a href="account/?lang=default" class="lang-link">
                  <div class="lang-link__flag"> <img class="image" src="assets/cy/images/svg/flags/flag--us.svg" alt=""> </div>
                  <div class="lang-link__text">EN</div>
                  </a> </div>
              </div>
            </div>
          <!--  <div class="topline-user-panel-block">-->
          <!--    <div class="topline-user-panel"> <a href="javascript:void(0)" class="topline-user" role="button" data-bs-toggle="dropdown"-->
										<!--aria-expanded="false">-->
          <!--      <div class="topline-user__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>-->
          <!--      <div class="topline-user__text"><?php echo isset($user->name) ? $user->name : 'User'; ?></div>-->
          <!--      </a>-->
          <!--      <div class="topline-user-dropdown dropdown-menu dropdown-menu-end"> <a href="profile.php" class="user-link">-->
          <!--        <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/user-icon.svg" alt=""> </div>-->
          <!--        <div class="user-link__text">Profile</div>-->
          <!--        </a> <a href="logout.php" class="user-link">-->
          <!--        <div class="user-link__icon"> <img class="image" src="assets/cy/images/svg/logout-icon.svg" alt=""> </div>-->
          <!--        <div class="user-link__text">Logout</div>-->
          <!--        </a> </div>-->
          <!--    </div>-->
          <!--  </div>--> 
              <div class="topline-logout-btn-block"> <a href="logout.php" class="topline-logout-btn">
                <div class="topline-logout-btn__icon"></div>
                </a> </div>
              <div class="mobile-panel-btn-block">
                <button type="button" class="mobile-panel-btn" onclick="show()"></button>
              </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

              <div class="db-page-content-block">
                <div class="db-page-content">
                  <div class="refill-block">
                    <div class="refill">
                      <div class="refill-content-block" style="width: 100%; padding:5px">
                        <div class="box">
                          <div class="h2 refill-form-title" style="background: linear-gradient(135deg, #17c964 0%, #0170ef 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 20px; font-size: 2.5rem; font-weight: 800; text-align: center; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);"><?php echo $title; ?></div>
                          <div class="refill-info-descr" style="margin-bottom: 35px; color: #a0a9b8; text-align: center; font-size: 1.1rem; font-weight: 400; line-height: 1.6;">
                              <i class="fas fa-sitemap me-2" style="color: #17c964;"></i>
                              Visualize your network structure and team hierarchy in an interactive tree format
                              <div style="margin-top: 10px; font-size: 0.9rem; color: #848e9c;">
                                  <i class="fas fa-info-circle me-1"></i>
                                  Click on any member to explore their downline • Hover for detailed statistics
                              </div>
                          </div>

                          <!-- Tree Container -->
                          <div class="tree-container">
                              <!-- Navigation Instructions -->
                              <!--<div class="tree-navigation-info">-->
                              <!--    <div class="row">-->
                              <!--        <div class="col-12 text-center">-->
                              <!--            <div class="navigation-card">-->
                              <!--                <i class="fas fa-hand-pointer navigation-icon"></i>-->
                              <!--                <h4>Interactive Tree Navigation</h4>-->
                              <!--                <p>Click on any member card to expand and view their downline tree. Use level selector to navigate through different levels.</p>-->
                              <!--                <div class="navigation-tips">-->
                              <!--                    <span class="tip"><i class="fas fa-mouse-pointer"></i> Click to expand tree</span>-->
                              <!--                    <span class="tip"><i class="fas fa-copy"></i> Click to copy ID</span>-->
                              <!--                    <span class="tip"><i class="fas fa-eye"></i> View details</span>-->
                              <!--                </div>-->
                              <!--            </div>-->
                              <!--        </div>-->
                              <!--    </div>-->
                              <!--</div>-->

                              <!-- Level Selection Dropdown -->
                              <div class="level-selector-container">
                                  <div class="row mb-4">
                                      <div class="col-md-6">
                                          <label style="color: #ffffff; margin-bottom: 12px; font-size: 16px; font-weight: 600; display: flex; align-items: center;">
                                              <i class="fas fa-layer-group me-2" style="color: #17c964;"></i>
                                              Select Level to View
                                          </label>
                                          <select class="form-control level-selector" style="color : white" id="levelSelector">
                                              <option value="0"  c>Root User</option>
                                              <?php
                                              $actualMaxLevel = getActualMaxLevel($uid, 20);
                                              for ($i = 1; $i <= $actualMaxLevel; $i++) {
                                                  $selected = ($i == 1) ? 'selected' : '';
                                                  echo '<option  style="color : black" value="' . $i . '" ' . $selected . '>Level ' . $i . '</option>';
                                              }
                                              ?>
                                          </select>
                                      </div>
                                      <div class="col-md-6">
                                          <label style="color: #ffffff; margin-bottom: 12px; font-size: 16px; font-weight: 600; display: flex; align-items: center;">
                                              <i class="fas fa-filter me-2" style="color: #0170ef;"></i>
                                              Filter by Status
                                          </label>
                                          <select class="form-control status-filter" style="color : white" id="statusFilter">
                                              <option value="all"  style="color : black">All Members</option>
                                              <option value="active"  style="color : black">Active Only</option>
                                              <option value="inactive"  style="color : black">Inactive Only</option>
                                              <option value="blocked"  style="color : black">Blocked Only</option>
                                          </select>
                                      </div>
                                  </div>
                              </div>

                              <!-- Tree View Area with Level-based Display -->
                              <div class="tree-view-area">
                                  <!-- Root User Display -->
                                  <div class="level-container" data-level="0">
                                      <div class="level-header">
                                          <h3><i class="fas fa-crown me-2"></i>Root User</h3>
                                      </div>
                                      <div class="users-grid">
                                          <div class="tree-user-card expandable-card <?php echo get_user_status_class($userF); ?>" data-user-id="<?php echo $uid; ?>" title="Click to expand tree">
                                              <div class="expand-indicator">
                                                  <i class="fas fa-chevron-down"></i>
                                                  <span>Click to expand</span>
                                              </div>
                                              <div class="copy-indicator">
                                                  <i class="fas fa-copy"></i>
                                                  <span>Copy ID</span>
                                              </div>
                                              <div class="tree-user-avatar">
                                                  <?php echo strtoupper(substr($userF->name ?? 'U', 0, 1)); ?>
                                              </div>
                                              <div class="tree-user-info">
                                                  <div class="tree-user-id">#<?php echo $userF->uid; ?></div>
                                                  <div class="tree-user-name"><?php echo $userF->name ?? 'User'; ?></div>
                                                  <div class="tree-user-status <?php echo get_user_status($userF); ?>">
                                                      <?php echo get_user_status($userF); ?>
                                                  </div>
                                                  <div class="tree-user-stats">
                                                      <span class="stat-item">
                                                          <i class="fas fa-users"></i>
                                                          Team: <?php echo get_count_child_ids($userF->uid, 'L') + get_count_child_ids($userF->uid, 'R'); ?>
                                                      </span>
                                                  </div>
                                              </div>
                                              <div class="tree-user-actions">
                                                  <button class="btn-view-details" onclick="event.stopPropagation(); showUserDetails(<?php echo $uid; ?>)">
                                                      <i class="fas fa-eye"></i>
                                                  </button>
                                                  <button class="btn-copy-id" onclick="event.stopPropagation(); copyUserIdOnly(<?php echo $uid; ?>)">
                                                      <i class="fas fa-copy"></i>
                                                  </button>
                                              </div>
                                          </div>

                                          <!-- Expandable Tree Container -->
                                          <div class="expandable-tree-container" id="tree-<?php echo $uid; ?>">
                                              <div class="tree-loading">
                                                  <i class="fas fa-spinner fa-spin"></i>
                                                  <span>Loading tree...</span>
                                              </div>
                                              <div class="tree-content"></div>
                                          </div>
                                      </div>
                                  </div>

                                  <!-- Dynamic Level Display -->
                                  <?php renderLevelBasedTree($uid, 20); // Increased from 10 to 20 levels ?>
                              </div>

                              <!-- User Details Modal -->
                              <div class="user-details-modal" id="userDetailsModal">
                                  <div class="modal-content">
                                      <div class="modal-header">
                                          <h4><i class="fas fa-user-circle me-2"></i>Member Details</h4>
                                          <button class="modal-close" onclick="closeUserDetails()">&times;</button>
                                      </div>
                                      <div class="modal-body" id="modalBody">
                                          <!-- Dynamic content will be loaded here -->
                                      </div>
                                  </div>
                              </div>
                          </div>

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</div>

<!-- Essential Libraries with CDN fallbacks -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

<!-- Mobile Sidebar Functions -->
<script>
    function show(){
       document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block active")
    }
    function closeSidebar(){
        document.querySelector(".mobile-panel-block").setAttribute("class", "mobile-panel-block")
    }
</script>

<!-- Try to load local assets, but don't fail if they're missing -->
<script>
// Load local assets if available, but don't break if they're missing
function loadScript(src) {
    var script = document.createElement('script');
    script.src = src;
    script.onerror = function() { console.log('Optional script failed to load:', src); };
    document.head.appendChild(script);
}

// Optional local scripts
loadScript('./assets/cy/js/common.js?vs=100');
loadScript('./sidebar.js');
</script>

<!-- Main Application Script -->
<script>
// Wait for jQuery to be available
(function() {
    function initializeApp() {
        if (typeof jQuery === 'undefined') {
            console.log('jQuery not available, retrying...');
            setTimeout(initializeApp, 100);
            return;
        }

        console.log('jQuery loaded successfully, version:', $.fn.jquery);

        /* Loop through all dropdown buttons to toggle between hiding and showing its dropdown content */
        var dropdown = document.getElementsByClassName("dropdown-btn");
        var i;

        for (i = 0; i < dropdown.length; i++) {
          dropdown[i].addEventListener("click", function() {
            this.classList.toggle("active");
            this.classList.toggle("dactive");
            var dropdownContent = this.nextElementSibling;
            if (dropdownContent.style.display === "block") {
              dropdownContent.style.display = "none";
            } else {
              dropdownContent.style.display = "block";
            }
          });
        }

        $(document).ready(function() {
            console.log('Document ready - Enhanced Tree View page initialized');

            // Enhanced tree user interactions
            $('.tree-user').on('click', function(e) {
                if (!$(e.target).closest('a').length) {
                    e.preventDefault();

                    // Add ripple effect
                    var ripple = $('<div class="ripple"></div>');
                    $(this).append(ripple);

                    var rect = this.getBoundingClientRect();
                    var size = Math.max(rect.width, rect.height);
                    var x = e.clientX - rect.left - size / 2;
                    var y = e.clientY - rect.top - size / 2;

                    ripple.css({
                        width: size,
                        height: size,
                        left: x,
                        top: y
                    }).addClass('animate');

                    setTimeout(function() {
                        ripple.remove();
                    }, 600);
                }
            });

            // Enhanced tooltip positioning with smart viewport detection
            $('.tree-user').hover(
                function(e) {
                    var tooltip = $(this).find('.tree-tooltip');
                    var rect = this.getBoundingClientRect();
                    var viewportWidth = window.innerWidth;
                    var viewportHeight = window.innerHeight;
                    var tooltipWidth = 350;
                    var tooltipHeight = 250;

                    // Calculate optimal position
                    var left = rect.left + (rect.width / 2) - (tooltipWidth / 2);
                    var top = rect.top - tooltipHeight - 20;

                    // Adjust if tooltip goes off screen horizontally
                    if (left < 20) {
                        left = 20;
                    } else if (left + tooltipWidth > viewportWidth - 20) {
                        left = viewportWidth - tooltipWidth - 20;
                    }

                    // Adjust if tooltip goes off screen vertically
                    if (top < 20) {
                        top = rect.bottom + 20;
                    }

                    // Position tooltip using fixed positioning
                    tooltip.css({
                        'left': left + 'px',
                        'top': top + 'px',
                        'position': 'fixed'
                    });

                    // Add entrance animation
                    tooltip.addClass('tooltip-show');
                },
                function() {
                    $(this).find('.tree-tooltip').removeClass('tooltip-show');
                }
            );

            // Enhanced search form interactions
            $('#login_id').on('focus', function() {
                $(this).parent().addClass('input-focused');
            }).on('blur', function() {
                $(this).parent().removeClass('input-focused');
            });

            // Add loading state to search button
            $('#form-validate').on('submit', function() {
                var btn = $('#submit');
                btn.html('<i class="fas fa-spinner fa-spin me-2"></i>Searching...');
                btn.prop('disabled', true);
            });

            // Smooth scroll to tree view area after search
            if (window.location.search.includes('no=')) {
                $('html, body').animate({
                    scrollTop: $('.tree-view-area').offset().top - 100
                }, 800);
            }

            // Level selector functionality
            $('#levelSelector').on('change', function() {
                var selectedLevel = $(this).val();

                // Store currently expanded trees to maintain state
                var expandedTrees = [];
                $('.expandable-card.expanded').each(function() {
                    expandedTrees.push($(this).data('user-id'));
                });

                // Close all expanded trees when changing levels
                $('.expandable-card.expanded').each(function() {
                    var card = $(this);
                    var userId = card.data('user-id');
                    var treeContainer = $('#tree-' + userId);
                    collapseTree(card, treeContainer);
                });

                // Add smooth transition effect
                $('.level-container.active').fadeOut(300, function() {
                    $('.level-container').removeClass('active');
                    var targetContainer = $('.level-container[data-level="' + selectedLevel + '"]');

                    if (targetContainer.length > 0) {
                        targetContainer.addClass('active').fadeIn(400, function() {
                            // Apply status filter after transition
                            setTimeout(function() {
                                applyStatusFilter();
                            }, 100);

                            // Update URL to reflect current level selection
                            updateUrlWithLevel(selectedLevel);

                            // Smooth scroll to the new level
                            if (selectedLevel >= 0) {
                                $('html, body').animate({
                                    scrollTop: targetContainer.offset().top - 120
                                }, 600);
                            }
                        });
                    } else {
                        // If level doesn't exist, show level 1 as fallback
                        $('#levelSelector').val('1');
                        $('.level-container[data-level="1"]').addClass('active').fadeIn(400);
                        updateUrlWithLevel('1');
                        applyStatusFilter();
                    }
                });
            });

            // Status filter functionality
            $('#statusFilter').on('change', function() {
                applyStatusFilter();
            });

            function applyStatusFilter() {
                var selectedStatus = $('#statusFilter').val();
                var activeLevel = $('.level-container.active');

                if (selectedStatus === 'all') {
                    activeLevel.find('.tree-user-card').show();
                } else {
                    activeLevel.find('.tree-user-card').hide();
                    activeLevel.find('.tree-user-card.' + selectedStatus).show();
                }
            }

            // Get level from URL parameter if available
            function getUrlParameter(name) {
                name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
                var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
                var results = regex.exec(location.search);
                return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
            }

            // Update URL with current level selection
            function updateUrlWithLevel(level) {
                var url = new URL(window.location);
                if (level && level !== '1') {
                    url.searchParams.set('level', level);
                } else {
                    url.searchParams.delete('level');
                }
                // Update URL without refreshing the page
                window.history.replaceState({}, '', url);
            }

            // Initialize with correct level (from URL or default to 1)
            var urlLevel = getUrlParameter('level');
            var initialLevel = urlLevel || '1';

            // Set the selector to the correct level
            $('#levelSelector').val(initialLevel);

            // Show the correct level container
            $('.level-container[data-level="' + initialLevel + '"]').addClass('active');

            // If URL level doesn't exist, fallback to level 1
            if (urlLevel && $('.level-container[data-level="' + initialLevel + '"]').length === 0) {
                $('#levelSelector').val('1');
                $('.level-container').removeClass('active');
                $('.level-container[data-level="1"]').addClass('active');
            }

            // Expandable card click functionality
            $(document).on('click', '.expandable-card', function(e) {
                // Prevent expanding when clicking action buttons
                if ($(e.target).closest('.tree-user-actions').length > 0) {
                    return;
                }

                var userId = $(this).data('user-id');
                var treeContainer = $('#tree-' + userId);
                var card = $(this);

                // Toggle expanded state
                if (card.hasClass('expanded')) {
                    // Collapse tree
                    collapseTree(card, treeContainer);
                } else {
                    // Close other expanded trees in the same level to avoid clutter
                    var currentLevel = card.closest('.level-container').data('level');
                    $('.level-container[data-level="' + currentLevel + '"] .expandable-card.expanded').each(function() {
                        if ($(this).data('user-id') !== userId) {
                            var otherCard = $(this);
                            var otherContainer = $('#tree-' + $(this).data('user-id'));
                            collapseTree(otherCard, otherContainer);
                        }
                    });

                    // Expand tree
                    expandTree(card, treeContainer, userId);

                    // Smooth scroll to expanded content after a delay
                    setTimeout(function() {
                        if (treeContainer.hasClass('expanded')) {
                            $('html, body').animate({
                                scrollTop: treeContainer.offset().top - 100
                            }, 600);
                        }
                    }, 900);
                }
            });

            // Add parallax effect to background elements
            $(window).on('scroll', function() {
                var scrolled = $(this).scrollTop();
                var parallax = $('.tree-container::before');
                var speed = scrolled * 0.5;
                parallax.css('transform', 'translateY(' + speed + 'px)');
            });

            // Handle browser back/forward navigation
            $(window).on('popstate', function(e) {
                var urlLevel = getUrlParameter('level') || '1';
                if ($('#levelSelector').val() !== urlLevel) {
                    $('#levelSelector').val(urlLevel).trigger('change');
                }
            });
        });

        // User details modal functions
        function showUserDetails(userId) {
            // You can customize this function to show user details
            var modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <div class="user-detail-item">
                    <strong>User ID:</strong> ${userId}
                </div>
                <div class="user-detail-item">
                    <strong>Loading details...</strong>
                </div>
            `;

            document.getElementById('userDetailsModal').classList.add('show');
        }

        function closeUserDetails() {
            document.getElementById('userDetailsModal').classList.remove('show');
        }

        // Close modal when clicking outside
        document.getElementById('userDetailsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeUserDetails();
            }
        });

        // Copy to clipboard function
        function copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                // Modern clipboard API
                navigator.clipboard.writeText(text).then(function() {
                    console.log('Text copied to clipboard successfully');
                }).catch(function(err) {
                    console.error('Failed to copy text: ', err);
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                // Fallback for older browsers
                fallbackCopyTextToClipboard(text);
            }
        }

        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            textArea.style.opacity = "0";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    console.log('Fallback: Text copied to clipboard successfully');
                } else {
                    console.error('Fallback: Failed to copy text');
                }
            } catch (err) {
                console.error('Fallback: Failed to copy text: ', err);
            }

            document.body.removeChild(textArea);
        }

        // Show copy notification
        function showCopyNotification(message) {
            // Remove existing notification if any
            var existingNotification = document.querySelector('.copy-notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            // Create new notification
            var notification = document.createElement('div');
            notification.className = 'copy-notification';
            notification.innerHTML = '<i class="fas fa-check-circle"></i>' + message;

            document.body.appendChild(notification);

            // Show notification
            setTimeout(function() {
                notification.classList.add('show');
            }, 100);

            // Hide and remove notification after 3 seconds
            setTimeout(function() {
                notification.classList.remove('show');
                setTimeout(function() {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 400);
            }, 3000);
        }

        // Copy User ID only function
        function copyUserIdOnly(userId) {
            copyToClipboard(userId.toString());
            showCopyNotification('User ID ' + userId + ' copied to clipboard!');
        }

        // Expand tree function
        function expandTree(card, treeContainer, userId) {
            // Add expanded class
            card.addClass('expanded');

            // Show loading
            treeContainer.find('.tree-loading').addClass('show');
            treeContainer.addClass('expanded');

            // Match the height of the main tree view area
            var mainTreeHeight = $('.tree-view-area').height();
            treeContainer.css('max-height', Math.min(mainTreeHeight * 0.8, window.innerHeight * 0.8) + 'px');

            // Simulate loading and fetch tree data
            setTimeout(function() {
                loadTreeData(userId, treeContainer);
            }, 800);
        }

        // Collapse tree function
        function collapseTree(card, treeContainer) {
            card.removeClass('expanded');
            treeContainer.removeClass('expanded');
            treeContainer.find('.tree-content').removeClass('loaded').empty();
            treeContainer.find('.tree-loading').removeClass('show');
            treeContainer.css('max-height', ''); // Reset max-height

            // Add a small delay to ensure smooth animation
            setTimeout(function() {
                treeContainer.scrollTop(0); // Reset scroll position
            }, 300);
        }

        // Load tree data function
        function loadTreeData(userId, treeContainer) {
            // Hide loading
            treeContainer.find('.tree-loading').removeClass('show');

            // Create tree content
            var treeContent = treeContainer.find('.tree-content');

            // Generate tree HTML (you can customize this based on your data)
            var treeHTML = generateTreeHTML(userId);

            treeContent.html(treeHTML);
            treeContent.addClass('loaded');
        }

        // Generate tree HTML function
        function generateTreeHTML(userId) {
            // Make AJAX call to get actual downline data
            $.ajax({
                url: 'get_downline_data.php',
                method: 'POST',
                data: { user_id: userId },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        displayDownlineTree(userId, response.data);
                    } else {
                        displayEmptyTree(userId);
                    }
                },
                error: function() {
                    // Fallback: generate tree from existing page data
                    generateTreeFromPageData(userId);
                }
            });

            return `
                <div class="mini-tree-view">
                    <div class="mini-tree-header">
                        <h5><i class="fas fa-sitemap me-2"></i>Downline for User #${userId}</h5>
                    </div>
                    <div class="mini-tree-content" id="mini-tree-${userId}">
                        <div class="loading-placeholder">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>Loading downline...</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // Generate tree from existing page data (fallback)
        function generateTreeFromPageData(userId) {
            // Get downline data from PHP
            $.ajax({
                url: window.location.href,
                method: 'POST',
                data: {
                    action: 'get_downline',
                    user_id: userId
                },
                success: function(response) {
                    try {
                        var data = JSON.parse(response);
                        displayDownlineTree(userId, data);
                    } catch(e) {
                        // Fallback to empty tree
                        displayEmptyTree(userId);
                    }
                },
                error: function() {
                    displayEmptyTree(userId);
                }
            });
        }

        // Display downline tree
        function displayDownlineTree(userId, downlineData) {
            var container = $('#mini-tree-' + userId);

            if (!downlineData || downlineData.length === 0) {
                displayEmptyTree(userId);
                return;
            }

            var html = '';
            var levels = organizeByLevels(downlineData);

            for (var level in levels) {
                if (levels[level].length > 0) {
                    html += `
                        <div class="downline-level">
                            <div class="level-title">
                                <i class="fas fa-layer-group"></i>
                                Level ${level} (${levels[level].length} members)
                            </div>
                            <div class="level-users">
                    `;

                    levels[level].forEach(function(user) {
                        html += generateUserCard(user);
                    });

                    html += `
                            </div>
                        </div>
                    `;
                }
            }

            container.html(html);
        }

        // Display empty tree
        function displayEmptyTree(userId) {
            var container = $('#mini-tree-' + userId);
            container.html(`
                <div class="empty-tree">
                    <i class="fas fa-users-slash"></i>
                    <h6>No Downline Found</h6>
                    <p>This user doesn't have any downline members yet.</p>
                </div>
            `);
        }

        // Organize users by levels
        function organizeByLevels(users) {
            var levels = {};
            users.forEach(function(user) {
                var level = user.level || 1;
                if (!levels[level]) {
                    levels[level] = [];
                }
                levels[level].push(user);
            });
            return levels;
        }

        // Generate individual user card
        function generateUserCard(user) {
            var statusClass = user.status == 1 ? 'blocked' : (user.topup > 0 ? 'active' : 'inactive');
            var statusText = user.status == 1 ? 'blocked' : (user.topup > 0 ? 'active' : 'inactive');

            return `
                <div class="mini-user-card ${statusClass}" onclick="navigateToUser(${user.uid})" title="Click to view ${user.name}'s tree">
                    <div class="mini-user-avatar">
                        ${user.name ? user.name.charAt(0).toUpperCase() : 'U'}
                    </div>
                    <div class="mini-user-info">
                        <div class="mini-user-id">#${user.uid}</div>
                        <div class="mini-user-name">${user.name || 'User'} - ${user.position == 'L' ? 'Left' : 'Right'}</div>
                        <div class="mini-user-status ${statusText}">${statusText}</div>
                        <div class="mini-user-meta">
                            <span><i class="fas fa-calendar"></i> ${formatDate(user.datetime)}</span>
                        </div>
                    </div>
                    <div class="mini-user-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            `;
        }

        // Navigate to user function with state preservation
        function navigateToUser(userId) {
            // Store current level selection in URL
            var currentLevel = $('#levelSelector').val();
            var url = 'tree_view.php?no=' + userId;
            if (currentLevel && currentLevel !== '1') {
                url += '&level=' + currentLevel;
            }
            window.location.href = url;
        }

        // Format date function
        function formatDate(dateString) {
            var date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }
    }

    // Initialize the app
    initializeApp();
})();
</script>

</body>
</html>

<?php

function get_img_clr($user) {
    $black = 3;
    $green = 2;
    $red = 4;
    if ($user->status == 1) {
        $img = $black;
    } elseif ($user->topup > 0) {
        $img = $green;
    } else {
        $img = $red;
    }
    return $img;
}

function get_user_status($user) {
    if ($user->status == 1) {
        return 'blocked';
    } elseif ($user->topup > 0) {
        return 'active';
    } else {
        return 'inactive';
    }
}

function get_user_status_class($user) {
    if ($user->status == 1) {
        return 'blocked';
    } elseif ($user->topup > 0) {
        return 'active';
    } else {
        return 'inactive';
    }
}
?>

<?php

function getTreeDescription($uid, $date, $refer_id) {
    // This function is kept for backward compatibility but not used in modern design
}

function renderModernTree($uid, $__i = 1, $__k = 0) {
    $__k++;
    $rs = my_query("SELECT * FROM user WHERE placement_id = '$uid'");
    $num = my_num_rows($rs);
    $parr = ['L' => 1, 'R' => 2];

    if ($num > 0 || count($parr) > 0) {
        echo '<div class="row justify-content-center" style="margin-top: 30px;">';

        // Process existing users
        if ($num) {
            while ($row = my_fetch_object($rs)) {
                $p = $row->position;
                $colClass = ($p == 'R') ? 'col-md-6 text-end' : 'col-md-6 text-start';
                unset($parr[$p]);
                ?>
                <div class="<?php echo $colClass; ?>">
                    <div class="tree-node">
                        <div class="tree-user <?php echo get_user_status_class($row); ?>" onclick="window.location.href='tree_view.php?no=<?php echo $row->uid; ?>'">
                            <div class="tree-user-avatar">
                                <?php echo strtoupper(substr($row->name ?? 'U', 0, 1)); ?>
                            </div>
                            <div class="tree-user-id">#<?php echo $row->uid; ?></div>
                            <div class="tree-user-name"><?php echo $row->name ?? 'User'; ?></div>
                            <div class="tree-user-status <?php echo get_user_status($row); ?>">
                                <?php echo get_user_status($row); ?>
                            </div>

                            <!-- Enhanced Tooltip -->
                            <div class="tree-tooltip">
                                <h4><i class="fas fa-user me-2"></i>Member Details</h4>
                                <div style="color: #eaecef; margin-bottom: 10px;">
                                    <div style="margin-bottom: 5px;"><strong>DOJ:</strong> <?php echo date("d M, Y", strtotime($row->datetime)); ?></div>
                                    <div style="margin-bottom: 5px;"><strong>Sponsor:</strong> <?php echo $row->refer_id; ?></div>
                                    <div style="margin-bottom: 5px;"><strong>Package:</strong> $<?php echo number_format($row->topup ?? 0, 2); ?></div>
                                    <div style="margin-bottom: 5px;"><strong>Position:</strong> <?php echo $row->position; ?></div>
                                </div>
                                <div class="tree-tooltip-stats">
                                    <div class="tree-tooltip-stat">
                                        <div class="tree-tooltip-stat-label">Left Team</div>
                                        <div class="tree-tooltip-stat-value"><?php echo get_count_child_ids($row->uid, 'L'); ?></div>
                                    </div>
                                    <div class="tree-tooltip-stat">
                                        <div class="tree-tooltip-stat-label">Right Team</div>
                                        <div class="tree-tooltip-stat-value"><?php echo get_count_child_ids($row->uid, 'R'); ?></div>
                                    </div>
                                    <div class="tree-tooltip-stat">
                                        <div class="tree-tooltip-stat-label">Left BV</div>
                                        <div class="tree-tooltip-stat-value"><?php echo number_format(get_child_bv_total($row->uid, 'L'), 0); ?></div>
                                    </div>
                                    <div class="tree-tooltip-stat">
                                        <div class="tree-tooltip-stat-label">Right BV</div>
                                        <div class="tree-tooltip-stat-value"><?php echo number_format(get_child_bv_total($row->uid, 'R'), 0); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php if ($__k < $__i) { ?>
                            <!-- Enhanced Connection Line -->
                            <div style="margin: 20px 0;">
                                <div style="width: 3px; height: 25px; background: linear-gradient(180deg, #17c964 0%, #0170ef 50%, #17c964 100%); margin: 0 auto; border-radius: 2px; box-shadow: 0 0 10px rgba(23, 201, 100, 0.3); position: relative;">
                                    <div style="position: absolute; top: 50%; left: 50%; width: 6px; height: 6px; background: radial-gradient(circle, #17c964 0%, #0170ef 100%); border-radius: 50%; transform: translate(-50%, -50%); box-shadow: 0 0 8px rgba(23, 201, 100, 0.5);"></div>
                                </div>
                            </div>
                            <?php renderModernTree($row->uid, $__i, $__k); ?>
                        <?php } ?>
                    </div>
                </div>
                <?php
            }
        }

        // Process empty positions
        foreach ($parr as $k => $v) {
            $colClass = ($k == 'R') ? 'col-md-6 text-end' : 'col-md-6 text-start';
            ?>
            <div class="<?php echo $colClass; ?>">
                <div class="tree-node">
                    <a href="tree_register.php?placement_id=<?php echo $uid; ?>&position=<?php echo $k; ?>" target="_blank" class="tree-new-user">
                        <i class="fas fa-plus"></i>
                        <div style="font-size: 13px; margin-top: 8px; font-weight: 600;">Add Member</div>
                        <div style="font-size: 11px; color: #a0a9b8; font-weight: 500; text-transform: uppercase; letter-spacing: 0.5px;"><?php echo $k; ?> Position</div>
                    </a>
                </div>
            </div>
            <?php
        }

        echo '</div>';
    }
}

function renderLevelBasedTree($uid, $maxLevels = 20) {
    // First, determine the actual maximum level with users
    $actualMaxLevel = getActualMaxLevel($uid, $maxLevels);

    for ($level = 1; $level <= $actualMaxLevel; $level++) {
        echo '<div class="level-container" data-level="' . $level . '">';
        echo '<div class="level-header">';
        echo '<h3><i class="fas fa-layer-group me-2"></i>Level ' . $level . '</h3>';
        echo '</div>';
        echo '<div class="users-grid">';

        $users = getLevelUsers($uid, $level);

        if (!empty($users)) {
            foreach ($users as $user) {
                $statusClass = get_user_status_class($user);
                $status = get_user_status($user);
                ?>
                <div class="tree-user-card expandable-card <?php echo $statusClass; ?>" data-user-id="<?php echo $user->uid; ?>" title="Click to expand tree">
                    <div class="expand-indicator">
                        <i class="fas fa-chevron-down"></i>
                        <span>Click to expand</span>
                    </div>
                    <div class="copy-indicator">
                        <i class="fas fa-copy"></i>
                        <span>Copy ID</span>
                    </div>
                    <div class="tree-user-avatar">
                        <?php echo strtoupper(substr($user->name ?? 'U', 0, 1)); ?>  
                    </div>
                    <div class="tree-user-info">
                        <div class="tree-user-id">#<?php echo $user->uid; ?></div>
                        <div class="tree-user-name"><?php echo $user->name ?? 'User'; ?> -  <?php echo $user->position == 'L' ? "Left" : "Right" ?></div>
                        <div class="tree-user-status <?php echo $status; ?>">
                            <?php echo $status; ?>
                        </div>
                        <div style="font-size: 12px; color: #a0a9b8; margin-top: 8px;">
                            <i class="fas fa-calendar me-1"></i>
                            <?php echo date("d M, Y", strtotime($user->datetime)); ?>
                        </div>
                        <div style="font-size: 12px; color: #a0a9b8; margin-top: 4px;">
                            <i class="fas fa-user-tie me-1"></i>
                            Sponsor: <?php echo $user->refer_id; ?>
                        </div>
                        <div class="tree-user-stats">
                            <span class="stat-item">
                                <i class="fas fa-users"></i>
                                Team: <?php echo get_count_child_ids($user->uid, 'L') + get_count_child_ids($user->uid, 'R'); ?>
                            </span>
                        </div>
                    </div>
                    <div class="tree-user-actions">
                        <button class="btn-view-details" onclick="event.stopPropagation(); showUserDetails(<?php echo $user->uid; ?>)" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-copy-id" onclick="event.stopPropagation(); copyUserIdOnly(<?php echo $user->uid; ?>)" title="Copy ID">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>

                <!-- Expandable Tree Container -->
                <div class="expandable-tree-container" id="tree-<?php echo $user->uid; ?>">
                    <div class="tree-loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>Loading tree...</span>
                    </div>
                    <div class="tree-content"></div>
                </div>
                <?php
            }
        } else {
            echo '<div style="text-align: center; color: #848e9c; padding: 40px;">';
            echo '<i class="fas fa-users" style="font-size: 48px; margin-bottom: 15px; opacity: 0.5;"></i>';
            echo '<h4>No members found at this level</h4>';
            echo '<p>This level is currently empty.</p>';
            echo '</div>';
        }

        echo '</div>';
        echo '</div>';
    }
}

function getLevelUsers($uid, $level) {
    $users = [];
    $currentLevel = [$uid];

    for ($i = 1; $i <= $level; $i++) {
        $nextLevel = [];
        foreach ($currentLevel as $parentId) {
            $rs = my_query("SELECT * FROM user WHERE placement_id = '$parentId'");
            while ($row = my_fetch_object($rs)) {
                if ($i == $level) {
                    $users[] = $row;
                }
                $nextLevel[] = $row->uid;
            }
        }
        $currentLevel = $nextLevel;
    }

    return $users;
}

function getDownlineData($uid, $maxLevels = 20) {
    $downlineUsers = [];
    $currentLevel = [$uid];

    for ($level = 1; $level <= $maxLevels; $level++) {
        $nextLevel = [];
        foreach ($currentLevel as $parentId) {
            $rs = my_query("SELECT uid, name, datetime, refer_id, topup, status, position FROM user WHERE placement_id = '$parentId' ORDER BY position, datetime");
            while ($row = my_fetch_object($rs)) {
                $row->level = $level;
                $downlineUsers[] = $row;
                $nextLevel[] = $row->uid;
            }
        }
        $currentLevel = $nextLevel;

        // If no users found at this level, break
        if (empty($currentLevel)) {
            break;
        }
    }

    return $downlineUsers;
}

function getActualMaxLevel($uid, $maxLevels = 20) {
    $currentLevel = [$uid];
    $actualMaxLevel = 0;

    for ($level = 1; $level <= $maxLevels; $level++) {
        $nextLevel = [];
        $hasUsers = false;

        foreach ($currentLevel as $parentId) {
            $rs = my_query("SELECT uid FROM user WHERE placement_id = '$parentId'");
            while ($row = my_fetch_object($rs)) {
                $hasUsers = true;
                $nextLevel[] = $row->uid;
            }
        }

        if ($hasUsers) {
            $actualMaxLevel = $level;
            $currentLevel = $nextLevel;
        } else {
            break;
        }
    }

    return max(1, $actualMaxLevel); // Return at least 1 level
}
?>

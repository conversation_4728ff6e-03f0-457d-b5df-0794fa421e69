<?php
session_start();

if (!isset($_POST['email'])) {
    echo json_encode(['success' => false, 'message' => 'Email is required']);
    exit;
}

$email = $_POST['email'];
$otp = rand(100000, 999999);
$type = isset($_POST['type']) ? $_POST['type'] : 'register';

// Store OTP in session for registration/withdrawal
$_SESSION['otp_email'] = strtolower($email);
$_SESSION['otp'] = $otp;
$_SESSION['otp_type'] = $type;
// Choose template based on type
if ($type === 'register') {
    $subject = 'Email Verification - Complete Your Registration';
    $htmlTemplate = '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Email Verification - OTP</title><style>body{font-family:sans-serif;background:#f4f7fa;margin:0;padding:0;} .container{max-width:600px;margin:40px auto;padding:20px;} .email-wrapper{background:#fff;border-radius:12px;box-shadow:0 4px 20px rgba(0,0,0,0.1);} .header{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);padding:40px 30px;text-align:center;color:#fff;} .header h1{font-size:28px;font-weight:600;margin-bottom:10px;} .content{padding:40px 30px;} .otp-container{background:linear-gradient(135deg,#f093fb 0%,#f5576c 100%);border-radius:10px;padding:30px;text-align:center;margin:30px 0;border:2px dashed #e74c3c;} .otp-label{font-size:14px;color:#fff;margin-bottom:10px;text-transform:uppercase;letter-spacing:1px;font-weight:600;} .otp-code{font-size:36px;font-weight:700;color:#fff;letter-spacing:8px;font-family:"Courier New",monospace;text-shadow:2px 2px 4px rgba(0,0,0,0.3);} .otp-validity{font-size:12px;color:#fff;margin-top:10px;opacity:0.9;} .footer{background:#f8f9fa;padding:30px;text-align:center;color:#6c757d;font-size:14px;} .company-name{font-weight:600;color:#495057;margin-bottom:10px;} @media only screen and (max-width:600px){.container{padding:10px;}.header,.content,.footer{padding:20px;}.header h1{font-size:24px;}.otp-code{font-size:28px;letter-spacing:4px;}}</style></head><body><div class="container"><div class="email-wrapper"><div class="header"><h1>Welcome to BRT Multisoft!</h1><p>Verify your email to complete registration</p></div><div class="content"><p>Thank you for signing up! Please use the OTP below to verify your email address and activate your account.</p><div class="otp-container"><div class="otp-label">Your Verification Code</div><div class="otp-code">' . $otp . '</div><div class="otp-validity">Valid for 10 minutes</div></div><p>If you did not request this, please ignore this email.</p></div><div class="footer"><div class="company-name">BRT Multisoft</div><div>Need help? <a href="mailto:<EMAIL>">Contact Support</a></div></div></div></div></body></html>';
} elseif ($type === 'forgot') {
    $subject = 'Password Reset Request - OTP Verification';
    $htmlTemplate = '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Password Reset - OTP</title><style>body{font-family:sans-serif;background:#f4f7fa;margin:0;padding:0;} .container{max-width:600px;margin:40px auto;padding:20px;} .email-wrapper{background:#fff;border-radius:12px;box-shadow:0 4px 20px rgba(0,0,0,0.1);} .header{background:linear-gradient(135deg,#ffb347 0%,#ffcc33 100%);padding:40px 30px;text-align:center;color:#fff;} .header h1{font-size:28px;font-weight:600;margin-bottom:10px;} .content{padding:40px 30px;} .otp-container{background:linear-gradient(135deg,#43cea2 0%,#185a9d 100%);border-radius:10px;padding:30px;text-align:center;margin:30px 0;border:2px dashed #185a9d;} .otp-label{font-size:14px;color:#fff;margin-bottom:10px;text-transform:uppercase;letter-spacing:1px;font-weight:600;} .otp-code{font-size:36px;font-weight:700;color:#fff;letter-spacing:8px;font-family:"Courier New",monospace;text-shadow:2px 2px 4px rgba(0,0,0,0.3);} .otp-validity{font-size:12px;color:#fff;margin-top:10px;opacity:0.9;} .footer{background:#f8f9fa;padding:30px;text-align:center;color:#6c757d;font-size:14px;} .company-name{font-weight:600;color:#495057;margin-bottom:10px;} @media only screen and (max-width:600px){.container{padding:10px;}.header,.content,.footer{padding:20px;}.header h1{font-size:24px;}.otp-code{font-size:28px;letter-spacing:4px;}}</style></head><body><div class="container"><div class="email-wrapper"><div class="header"><h1>Password Reset Request</h1><p>OTP for Password Reset</p></div><div class="content"><p>We received a request to reset your password. Use the OTP below to proceed. If you did not request a password reset, please ignore this email.</p><div class="otp-container"><div class="otp-label">Password Reset OTP</div><div class="otp-code">' . $otp . '</div><div class="otp-validity">Valid for 10 minutes</div></div></div><div class="footer"><div class="company-name">BRT Multisoft</div><div>Need help? <a href="mailto:<EMAIL>">Contact Support</a></div></div></div></div></body></html>';
} elseif ($type === 'withdrawal') {
    $subject = 'Withdrawal Request - OTP Confirmation';
    $htmlTemplate = '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Withdrawal OTP</title><style>body{font-family:sans-serif;background:#f4f7fa;margin:0;padding:0;} .container{max-width:600px;margin:40px auto;padding:20px;} .email-wrapper{background:#fff;border-radius:12px;box-shadow:0 4px 20px rgba(0,0,0,0.1);} .header{background:linear-gradient(135deg,#11998e 0%,#38ef7d 100%);padding:40px 30px;text-align:center;color:#fff;} .header h1{font-size:28px;font-weight:600;margin-bottom:10px;} .content{padding:40px 30px;} .otp-container{background:linear-gradient(135deg,#fc5c7d 0%,#6a82fb 100%);border-radius:10px;padding:30px;text-align:center;margin:30px 0;border:2px dashed #6a82fb;} .otp-label{font-size:14px;color:#fff;margin-bottom:10px;text-transform:uppercase;letter-spacing:1px;font-weight:600;} .otp-code{font-size:36px;font-weight:700;color:#fff;letter-spacing:8px;font-family:"Courier New",monospace;text-shadow:2px 2px 4px rgba(0,0,0,0.3);} .otp-validity{font-size:12px;color:#fff;margin-top:10px;opacity:0.9;} .footer{background:#f8f9fa;padding:30px;text-align:center;color:#6c757d;font-size:14px;} .company-name{font-weight:600;color:#495057;margin-bottom:10px;} @media only screen and (max-width:600px){.container{padding:10px;}.header,.content,.footer{padding:20px;}.header h1{font-size:24px;}.otp-code{font-size:28px;letter-spacing:4px;}}</style></head><body><div class="container"><div class="email-wrapper"><div class="header"><h1>Withdrawal Request Confirmation</h1><p>OTP for Withdrawal</p></div><div class="content"><p>You have requested a withdrawal from your BRT Multisoft account. Please use the OTP below to confirm your withdrawal. If you did not request this, please contact support immediately.</p><div class="otp-container"><div class="otp-label">Withdrawal OTP</div><div class="otp-code">' . $otp . '</div><div class="otp-validity">Valid for 10 minutes</div></div></div><div class="footer"><div class="company-name">BRT Multisoft</div><div>Need help? <a href="mailto:<EMAIL>">Contact Support</a></div></div></div></div></body></html>';
} elseif ($type === 'bep20_update') {
    $subject = 'BEP20 Address Update - Security Verification';
    $htmlTemplate = '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>BEP20 Address Update OTP</title><style>body{font-family:sans-serif;background:#f4f7fa;margin:0;padding:0;} .container{max-width:600px;margin:40px auto;padding:20px;} .email-wrapper{background:#fff;border-radius:12px;box-shadow:0 4px 20px rgba(0,0,0,0.1);} .header{background:linear-gradient(135deg,#f093fb 0%,#f5576c 100%);padding:40px 30px;text-align:center;color:#fff;} .header h1{font-size:28px;font-weight:600;margin-bottom:10px;} .content{padding:40px 30px;} .otp-container{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);border-radius:10px;padding:30px;text-align:center;margin:30px 0;border:2px dashed #764ba2;} .otp-label{font-size:14px;color:#fff;margin-bottom:10px;text-transform:uppercase;letter-spacing:1px;font-weight:600;} .otp-code{font-size:36px;font-weight:700;color:#fff;letter-spacing:8px;font-family:"Courier New",monospace;text-shadow:2px 2px 4px rgba(0,0,0,0.3);} .otp-validity{font-size:12px;color:#fff;margin-top:10px;opacity:0.9;} .footer{background:#f8f9fa;padding:30px;text-align:center;color:#6c757d;font-size:14px;} .company-name{font-weight:600;color:#495057;margin-bottom:10px;} @media only screen and (max-width:600px){.container{padding:10px;}.header,.content,.footer{padding:20px;}.header h1{font-size:24px;}.otp-code{font-size:28px;letter-spacing:4px;}}</style></head><body><div class="container"><div class="email-wrapper"><div class="header"><h1>BEP20 Address Update</h1><p>Security Verification Required</p></div><div class="content"><p>You are attempting to update your USDT BEP20 address. For security purposes, please use the OTP below to verify this change. If you did not request this update, please contact support immediately.</p><div class="otp-container"><div class="otp-label">Address Update OTP</div><div class="otp-code">' . $otp . '</div><div class="otp-validity">Valid for 10 minutes</div></div><p><strong>Security Notice:</strong> Your BEP20 address can only be updated after email verification for security reasons.</p></div><div class="footer"><div class="company-name">BRT Multisoft</div><div>Need help? <a href="mailto:<EMAIL>">Contact Support</a></div></div></div></div></body></html>';
} else {
    $subject = 'Your OTP Code';
    $htmlTemplate = '<div>Your OTP code is: <b>' . $otp . '</b></div>';
}

// Prepare ZeptoMail API call with HTML template
$payload = json_encode([
    "from" => ["address" => "<EMAIL>", "name" => "BRT Multisoft"],
    "to" => [["email_address" => ["address" => $email, "name" => "User"]]],
    "subject" => $subject,
    "htmlbody" => $htmlTemplate
]);

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => "https://api.zeptomail.in/v1.1/email",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_CUSTOMREQUEST => "POST",
    CURLOPT_POSTFIELDS => $payload,
    CURLOPT_HTTPHEADER => [
        "accept: application/json",
        "authorization: Zoho-enczapikey PHtE6r1YS+jqjTYq+kID5PXqR8KhNY9//ONmeQdDtNpEW/ZSG00Bq4wukDbi+k0iUvJFRv/Owd9s4+/I5+uDJTvoZmsdW2qyqK3sx/VYSPOZsbq6x00csVQbdU3VXILvcN9u1SzVvtmX",
        "cache-control: no-cache",
        "content-type: application/json",
    ],
]);

$response = curl_exec($curl);
$err = curl_error($curl);
curl_close($curl);

if ($err) {
    echo json_encode(['success' => false, 'message' => "cURL Error: $err"]);
} else {
    $responseData = json_decode($response, true);
    
    if (isset($responseData['error'])) {
        echo json_encode(['success' => false, 'message' => 'Email sending failed: ' . $responseData['error']['message']]);
    } else {
        echo json_encode(['success' => true, 'message' => 'OTP sent successfully']);
    }
}
?>
<?php include_once '../lib/config.php';
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(-1);
$tmarr = array(0, 20, 40, 60, 120, 500, 1000);
$dmarr = array(0, 2, 3, 5, 10, 50, 100);
$time_arr = array(0, 15, 30, 50, 70, 100, 150);
$amtarr = array(0, 30, 50, 70, 100, 500, 1000);
$amtarr2 = array(0, 5, 6, 7, 15, 30);
$team_arr = array(0, 2, 4, 6, 15, 30);

$current_week_start = date('Y-m-d', strtotime('sunday -6 days')); 
$current_week_end = date('Y-m-d'); 

echo "Processing Weekly Rewards for week: " . $current_week_start . " to " . $current_week_end . " (Sunday to Sunday)<br/>";

$weekly_rewards = array(
    5 => 50,   
    10 => 100,
    25 => 300 
);

$total_rewards_given = 0;
$total_users_rewarded = 0;


$result = mysqli_query($link, "SELECT * FROM user WHERE topup > 0");

while ($row = mysqli_fetch_object($result)) {
    $uid = $row->uid;
    $login_id = $row->login_id;
    
    

    $direct_count_query = "SELECT COUNT(*) as direct_count FROM user WHERE refer_id = '".$uid."' AND topup > 0 AND status = 0 AND DATE(datetime) >= '".$current_week_start."' AND DATE(datetime) <= '".$current_week_end."'";
    $direct_result = mysqli_query($link, $direct_count_query);
    $direct_data = mysqli_fetch_object($direct_result);
    $direct_count = $direct_data->direct_count;

    if($direct_count > 0) {
        echo "User ".$login_id." (UID: ".$uid.") - NEW Direct referrals this week: ".$direct_count."<br/>";
    }
    
    
    $weekly_reward_amount = 0;
    $reward_level = 0;
    $remarks = "";
    
    if($direct_count >= 25) {
        $weekly_reward_amount = 300;
        $reward_level = 3;
        $remarks = "25 NEW Direct ID This Week (Sunday to Sunday) - Weekly Reward";
    }
    elseif($direct_count >= 10) {
        $weekly_reward_amount = 100;
        $reward_level = 2;
        $remarks = "10 NEW Direct ID This Week (Sunday to Sunday) - Weekly Reward";
    }
    elseif($direct_count >= 5) {
        $weekly_reward_amount = 50;
        $reward_level = 1;
        $remarks = "5 NEW Direct ID This Week (Sunday to Sunday) - Weekly Reward";
    }
    
    
    if($weekly_reward_amount > 0) {
       
        $check_weekly_reward = my_num_rows(my_query("SELECT recid FROM income_reward WHERE uid='".$uid."' AND reward='".$reward_level."' AND type=1 AND datetime >= '".$current_week_start." 00:00:00' AND datetime <= '".$current_week_end." 23:59:59'"));
        
        if(!$check_weekly_reward) {
          
                $weekly_reward_amount = check_3x($uid, $weekly_reward_amount);
            
            
            if($weekly_reward_amount > 0) {
              
                my_query("UPDATE user SET wallet = wallet + '".$weekly_reward_amount."' WHERE uid='".$uid."'");
                
            
                my_query("INSERT INTO income_reward (uid, reward,amount, datetime, type, status) VALUES ('".$uid."', '".$reward_level."','".$weekly_reward_amount."', '".date('Y-m-d H:i:s')."', 1, 1)");
                
                $total_rewards_given += $weekly_reward_amount;
                $total_users_rewarded++;
                
        
            } else {
                echo "User ".$login_id." (UID: ".$uid.") qualified but reward blocked by 3x limit - Direct count: ".$direct_count."<br/>";
            }
        } else {
            echo "User ".$login_id." (UID: ".$uid.") already received weekly reward for this week - Direct count: ".$direct_count."<br/>";
        }
    }

    // Tour Reward processing is now handled in cron_tb.php (daily cron)
    // No need to process Tour Rewards here anymore

    // $reward = $row->reward;
    
    // $_l = 0;
    // $new_amount = 0;
    // if($row->teamc >= 21070){
    //     $_l = 9;
    //     $new_amount = 6000;
    // }
    // elseif($row->teamc >= 11070){
    //     $_l = 8;
    //     $new_amount = 3000;
    // }
    // elseif($row->teamc >= 5070){
    //     $_l = 7;
    //     $new_amount = 1500;
    // }
    // elseif($row->teamc >= 2070){
    //     $_l = 6;
    //     $new_amount = 600;
    // }
    // elseif($row->teamc >= 1070){
    //     $_l = 5;
    //     $new_amount = 400;
    // }
    // elseif($row->teamc >= 470){
    //     $_l = 4;
    //     $new_amount = 300;
    // }
    // elseif($row->teamc >= 170){
    //     $_l = 3;
    //     $new_amount = 200;
    // }
    // elseif($row->teamc >= 70){
    //     $_l = 2;
    //     $new_amount = 125;
    // }
    // elseif($row->teamc >= 20){
    //     $_l = 1;
    //     $new_amount = 70;
    // }
    
    // if($_l > 0){
    //     royalty_insert($uid, $new_amount, $_l, 0);
    // }
    
    // foreach($tmarr as $k => $v){
    //     if($k){
    //         $datetime_bs = date('Y-m-d H:i:s', strtotime('+'.$v.' days', strtotime($row->datetime)));
    //         $check_d = my_num_rows(my_query("SELECT uid FROM user WHERE refer_id='".$uid."' AND status = 0 AND topup > 0 AND topup_datetime <= '".$datetime_bs."' "));
    //         if($check_d >= $v){
    //             $new_amount = $amtarr2[$k];
    //             royalty_insert($uid, $new_amount, $k, 1);
    //         }
    //     }
    // }
    
    // foreach($tmarr as $k => $v){
    //     if($k){
    //         $days = $time_arr[$k];
    //         $datetime_bs = date('Y-m-d H:i:s', strtotime('+'.$days.' days', strtotime($row->datetime)));
    //         $check_d = my_num_rows(my_query("SELECT uid FROM user WHERE refer_id='".$uid."' AND status = 0 AND topup > 0 AND topup_datetime <= '".$datetime_bs."' "));
    //       if($row->teamc >= $v && $check_d >= $dmarr[$k]){
    //             $new_amount = $amtarr[$k];
    //             royalty_insert($uid, $new_amount, $k, 2);
    //         }
    //     }
    // }
}

function royalty_insert($uid, $new_amount, $l, $type = 0, $n = 0, $tamt = 0){
    $new_amount = check_3x($uid, $new_amount);
    
    if($type == 2 && $new_amount > 0){
        $check = my_num_rows(my_query("SELECT uid FROM income_royalty WHERE uid='".$uid."' AND type = '".$type."' AND level = '".$l."'"));
        if(!$check){
            my_query("UPDATE user SET wallet= wallet+'$new_amount' WHERE uid='".$uid."'");
            my_query("INSERT INTO `income_royalty` (`uid`, `amount`, `datetime`, `level`, `type`) VALUES ('".$uid ."', '".$new_amount."', '".date('c')."', '".$l."', '".$type."')");
        }
    }
    elseif($type == 1 && $new_amount > 0){
        $check = my_num_rows(my_query("SELECT uid FROM income_royalty WHERE uid='".$uid."' AND type = '".$type."' AND level = '".$l."'"));
        if(!$check){
            my_query("UPDATE user SET wallet= wallet+'$new_amount' WHERE uid='".$uid."'");
            my_query("INSERT INTO `income_royalty` (`uid`, `amount`, `datetime`, `level`, `type`) VALUES ('".$uid ."', '".$new_amount."', '".date('c')."', '".$l."', '".$type."')");
        }
    }
    elseif($type == 0 && $new_amount > 0){
        $check = my_num_rows(my_query("SELECT uid FROM income_royalty WHERE uid='".$uid."' AND type = '".$type."' AND level = '".$l."'"));
        if(!$check){
            $days = $check+1;
            my_query("UPDATE user SET wallet= wallet+'$new_amount' WHERE uid='".$uid."'");
            my_query("INSERT INTO `income_royalty` (`uid`, `amount`, `datetime`, `level`, `type`, tamt, tid, days) VALUES ('".$uid ."', '".$new_amount."', '".date('c')."', '".$l."', '".$type."', '".($tamt)."', '".$n."', '".$days."')");
        }
    }
}

//if($date == date('Y-m-d')){
    //my_query("UPDATE investments SET statusc=1 WHERE statusc=0");
//}
echo "<br/> Weekly rewards processing complete (Sunday to Sunday). Please close this browser.";
?>
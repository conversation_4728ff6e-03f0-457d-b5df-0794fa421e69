<?php
// Test script to demonstrate Tour Reward calculation

echo "<h3>Tour Reward (Europe Tour) Calculation Test</h3>";

echo "<strong>Requirements:</strong><br/>";
echo "• Total Team Business: $10,000<br/>";
echo "• Business Distribution: 50% from STRONGEST direct user + 50% from ALL OTHER direct users<br/><br/>";

echo "<strong>Logic:</strong><br/>";
echo "1. Find all direct users and their team business<br/>";
echo "2. Sort by team business (highest to lowest)<br/>";
echo "3. Take 50% from the BIGGEST direct user<br/>";
echo "4. Take 50% from the SUM of ALL OTHER direct users<br/>";
echo "5. Add both together and check if ≥ $10,000<br/><br/>";

// Test scenarios with direct users
$test_scenarios = [
    [
        'name' => 'Qualified User - Strong Direct + Others',
        'direct_users' => [
            ['uid' => 101, 'teamb' => 15000], // Strongest direct
            ['uid' => 102, 'teamb' => 8000],  // Other direct
            ['uid' => 103, 'teamb' => 5000],  // Other direct
            ['uid' => 104, 'teamb' => 2000]   // Other direct
        ]
    ],
    [
        'name' => 'Not Qualified - Low Business',
        'direct_users' => [
            ['uid' => 201, 'teamb' => 6000],  // Strongest direct
            ['uid' => 202, 'teamb' => 3000],  // Other direct
            ['uid' => 203, 'teamb' => 1000]   // Other direct
        ]
    ],
    [
        'name' => 'Not Qualified - Only One Strong Direct',
        'direct_users' => [
            ['uid' => 301, 'teamb' => 18000], // Only one direct user
        ]
    ],
    [
        'name' => 'Qualified User - Multiple Directs',
        'direct_users' => [
            ['uid' => 401, 'teamb' => 12000], // Strongest direct
            ['uid' => 402, 'teamb' => 10000], // Other direct
            ['uid' => 403, 'teamb' => 8000],  // Other direct
            ['uid' => 404, 'teamb' => 6000],  // Other direct
            ['uid' => 405, 'teamb' => 4000]   // Other direct
        ]
    ],
    [
        'name' => 'Exactly $10K Tour Business',
        'direct_users' => [
            ['uid' => 501, 'teamb' => 16000], // Strongest: 16000 * 0.5 = 8000
            ['uid' => 502, 'teamb' => 4000]   // Others: 4000 * 0.5 = 2000, Total = 10000
        ]
    ]
];

foreach($test_scenarios as $scenario) {
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
    echo "<strong>" . $scenario['name'] . "</strong><br/>";

    $direct_users = $scenario['direct_users'];

    // Sort direct users by team business (highest to lowest) - already sorted in test data
    // In real implementation, this is done by SQL ORDER BY teamb DESC

    $tour_business = 0;
    $strongest_direct_business = 0;
    $other_directs_sum = 0;

    if(count($direct_users) > 0) {
        // Step 1: Get 50% from the strongest direct user (first in array)
        $strongest_direct_business = $direct_users[0]['teamb'];
        $strongest_50_percent = $strongest_direct_business * 0.5;

        // Step 2: Get 50% from sum of all other direct users
        for($i = 1; $i < count($direct_users); $i++) {
            $other_directs_sum += $direct_users[$i]['teamb'];
        }
        $others_50_percent = $other_directs_sum * 0.5;

        // Step 3: Tour business = 50% strongest + 50% others
        $tour_business = $strongest_50_percent + $others_50_percent;

        echo "<strong>Direct Users (sorted by team business):</strong><br/>";
        foreach($direct_users as $index => $user) {
            $label = ($index == 0) ? " (STRONGEST)" : " (Other)";
            echo "• User " . $user['uid'] . ": $" . number_format($user['teamb'], 2) . $label . "<br/>";
        }
        echo "<br/>";

        echo "Strongest Direct Business: $" . number_format($strongest_direct_business, 2) . "<br/>";
        echo "Other Directs Sum: $" . number_format($other_directs_sum, 2) . "<br/>";
        echo "Tour Business Calculation:<br/>";
        echo "• 50% from Strongest: $" . number_format($strongest_direct_business, 2) . " × 0.5 = $" . number_format($strongest_50_percent, 2) . "<br/>";
        echo "• 50% from Others: $" . number_format($other_directs_sum, 2) . " × 0.5 = $" . number_format($others_50_percent, 2) . "<br/>";
        echo "<strong>Total Tour Business: $" . number_format($tour_business, 2) . "</strong><br/>";
    } else {
        echo "No direct users found.<br/>";
    }

    if($tour_business >= 10000) {
        echo "<span style='color: green; font-weight: bold;'>✅ ELIGIBLE for Tour Reward</span><br/>";
    } else {
        echo "<span style='color: red; font-weight: bold;'>❌ NOT ELIGIBLE</span> (Need $" . number_format(10000 - $tour_business, 2) . " more)<br/>";
    }

    echo "</div>";
}

echo "<br/><strong>Database Field Mapping (Uses Existing Fields):</strong><br/>";
echo "<div style='background: #f5f5f5; padding: 10px; margin: 10px 0;'>";
echo "<strong>No new columns needed! Uses existing fields:</strong><br/>";
echo "<code>";
echo "• tbl (field 61): Stores strongest direct user's team business<br/>";
echo "• tbr (field 62): Stores sum of other direct users' team business<br/>";
echo "• got (field 65): Tour eligibility flag (0=not eligible, 1=eligible)<br/>";
echo "• teamb (field 59): Total team business (already calculated)<br/>";
echo "</code><br/><br/>";

echo "<strong>1. Check eligible users:</strong><br/>";
echo "<code>";
echo "SELECT uid, login_id, tbl as strongest_direct, tbr as other_directs, got as tour_eligible<br/>";
echo "FROM user<br/>";
echo "WHERE got = 1;";
echo "</code><br/><br/>";

echo "<strong>2. Check Tour Rewards given:</strong><br/>";
echo "<code>";
echo "SELECT ir.*, u.login_id, u.tbl as strongest_direct, u.tbr as other_directs<br/>";
echo "FROM income_reward ir<br/>";
echo "JOIN user u ON ir.uid = u.uid<br/>";
echo "WHERE ir.reward = 99 AND ir.type = 2<br/>";
echo "ORDER BY ir.datetime DESC;";
echo "</code><br/><br/>";

echo "<strong>3. Calculate tour business for a user:</strong><br/>";
echo "<code>";
echo "SELECT uid, login_id, tbl, tbr, (tbl * 0.5 + tbr * 0.5) as tour_business<br/>";
echo "FROM user<br/>";
echo "WHERE uid = 'USER_ID';";
echo "</code>";
echo "</div>";

echo "<br/><strong>Cron Schedule:</strong><br/>";
echo "• <strong>Daily</strong> (cron_tb.php): Calculates tour business AND gives rewards directly<br/>";
echo "• <strong>Weekly</strong> (cron_weekly_rewards.php): Only processes weekly direct referral rewards<br/><br/>";

echo "<strong>Database Storage:</strong><br/>";
echo "• <strong>income_reward table</strong>: reward=99, type=2 for Tour Rewards<br/>";
echo "• <strong>No intermediate storage</strong>: Calculation and reward in one process<br/>";
echo "• <strong>Direct reward</strong>: Eligible users get reward immediately in daily cron<br/>";
?>

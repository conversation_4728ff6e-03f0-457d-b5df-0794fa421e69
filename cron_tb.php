<?php include_once '../lib/config.php';
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(-1);

my_query("UPDATE `user` SET `teamb`=0,`teamc`=0 WHERE 1");

$result = my_query("SELECT uid, topup, reward, package FROM user WHERE status = 0 ORDER BY datetime DESC");
while ($row = my_fetch_object($result)) {
    $uid = $row->uid;
    if($row->topup > 0){
        $tb = get_sum('investments', 'amount', "uid = '".$uid."' AND ipid <= 8");
        $top = get_top_level_uids2($uid, 25000);
        //$top = get_top_level_uids($uid, 25000);
        foreach($top as $v){
            my_query("UPDATE user SET teamb = teamb + '".$tb."', teamc = teamc + 1 WHERE uid = '$v'");
        }
        
        $check_d = my_num_rows(my_query("SELECT uid FROM user WHERE refer_id='".$uid."' AND status = 0 AND topup > 0"));
        $reward = $row->reward;
            
        if($row->package >= 9 && $check_d >= 500){
            $reward = 9;
        }
        elseif($row->package >= 8 && $check_d >= 200){
            $reward = 8;
        }
        elseif($row->package >= 7 && $check_d >= 100){
            $reward = 7;
        }
        elseif($row->package >= 6 && $check_d >= 50){
            $reward = 6;
        }
        elseif($row->package >= 5 && $check_d >= 30){
            $reward = 5;
        }
        elseif($row->package >= 4 && $check_d >= 20){
            $reward = 4;
        }
        elseif($row->package >= 3 && $check_d >= 10){
            $reward = 3;
        }
        elseif($row->package >= 2 && $check_d >= 5){
            $reward = 2;
        }
        elseif($row->package >= 1 && $check_d >= 0){
            $reward = 1;
        }
        my_query("UPDATE user SET reward = '".$reward."' WHERE uid = '$uid'");
    }
    
    // $l = get_child_bv_total2($uid, 'L');
    // $r = get_child_bv_total2($uid, 'R');
    // my_query("UPDATE user SET tbl = '".$l."', tbr = '".$r."' WHERE uid = '$uid'");
    
    
     $direct_users_query = "SELECT uid FROM user WHERE refer_id = '".$uid."' AND status = 0 AND topup > 0";
    $direct_users_result = my_query($direct_users_query);

    $direct_users = array();
    while($direct_user = my_fetch_object($direct_users_result)) {
        $direct_uid = $direct_user->uid;

        $own_business = get_sum('investments', 'amount', "uid = '".$direct_uid."' AND ipid <= 8");
        $downline_business = calculate_downline_business($direct_uid);
        $team_business = $own_business + $downline_business;

        $direct_users[] = array(
            'uid' => $direct_uid,
            'teamb' => $team_business
        );
    }
    
    usort($direct_users, function($a, $b) {
        return $b['teamb'] - $a['teamb'];
    });
    
    $tour_business = 0;
    $strongest_direct_business = 0;
    $other_directs_sum = 0;
    
    if(count($direct_users) > 0) {
        
        $strongest_direct_business = $direct_users[0]['teamb'];
        $strongest_50_percent = $strongest_direct_business * 0.5;

        
        for($i = 1; $i < count($direct_users); $i++) {
            $other_directs_sum += $direct_users[$i]['teamb'];
        }
        $others_50_percent = $other_directs_sum * 0.5;
        if($strongest_50_percent >= 5000 && $others_50_percent >= 5000){
            
            $tour_business = $strongest_50_percent + $others_50_percent;
    
            if($tour_business >= 10000) {
                
                $check_tour_reward = my_num_rows(my_query("SELECT recid FROM income_reward WHERE uid='".$uid."' AND reward=99 AND type=2"));
    
                if(!$check_tour_reward) {
                  
                 my_query("INSERT INTO income_reward (uid, reward, datetime, type, status) VALUES ('".$uid."', 99, '".date('Y-m-d H:i:s')."', 2, 1)");
    
                    echo "✅ Tour Reward (Europe Tour) ELIGIBILITY recorded for user UID: ".$uid." - Tour Business: $".number_format($tour_business, 2)." (Strongest: $".number_format($strongest_direct_business, 2).", Others: $".number_format($other_directs_sum, 2).")<br/>";
                } else {
                    echo "User UID: ".$uid." already marked as eligible for Tour Reward<br/>";
                }    
                
            }
        }
    }
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    if($reward >= 3){
        $top = get_top_level_uids2($uid, 3);
        
        $level_amount = array(
            3 => array(50, 15, 5),
            4 => array(200, 120, 20),
            5 => array(600, 180, 60),
            6 => array(900, 300, 90),
            7 => array(1200, 400, 120),
            8 => array(1800, 600, 150),
            9 => array(2000, 700, 200)
        );
    
        $i = 0;
        $level = count($top);
        if($level>3){$level=3;}
        if($level>0){
            while($i<$level){
                $value = $top[$i];
                $user2 = get_user_details($value);
                $_reward = 3;
                while($_reward < $reward){
                    if($user2->reward >= $_reward){
                        $new_amount = $level_amount[$_reward][$i];
                        $new_amount = check_3x($value, $new_amount);
                        if($new_amount > 0){
                            $check = my_num_rows(my_query("SELECT uid FROM income_level WHERE uid =  '".$value."' AND from_uid = '".$uid."' AND type = 0 AND level = '".($i+1)."' AND pool = '".$_reward."'"));
                            if(!$check){
                                my_query("UPDATE user SET wallet= wallet+'$new_amount' WHERE uid='".$value."'");
                                my_query("INSERT INTO `income_level` (`uid`, `from_uid`, `amount`, `datetime`, `level`, ipid, iamount, pool, type) VALUES ('" .$value ."','".$uid."','".$new_amount."','".date('c')."','".($i+1)."', '0', '0','".($_reward)."', 0)");
                            }
                        }
                    }
                    $_reward++;
                }
                $i++;
            }
        }
    }
}


function calculate_downline_business($uid) {
    $total_downline_business = 0;

    
    $children_query = "SELECT uid FROM user WHERE refer_id = '$uid' AND status = 0 AND topup > 0";
    $children_result = my_query($children_query);

    while($child = my_fetch_object($children_result)) {
        $child_uid = $child->uid;

       
        $child_business = get_sum('investments', 'amount', "uid = '".$child_uid."' AND ipid <= 8");

      
        $total_downline_business += $child_business;

       
        $total_downline_business += calculate_downline_business($child_uid);
    }

    return $total_downline_business;
}





/* get top level uids */
function get_top_level_uids2($uid, $level=0, $arr=array()){
    global $link;
    $result = my_query("SELECT refer_id FROM user WHERE uid = '$uid'");
    if(count($arr)==$level && $level!=0){
        return $arr;
    }elseif($uid==100){
        return $arr;
    }
    if(my_num_rows($result)>0){
        $data = my_fetch_array($result);
        $arr[count($arr)] = $data[0];
        return get_top_level_uids2($data[0], $level, $arr);
    }else {
        return $arr;
    }
}
echo "<br/> Closing complete. Please close this browser.";
?>
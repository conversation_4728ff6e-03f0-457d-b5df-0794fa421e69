# Tour Reward (Europe Tour) Implementation

## Overview
This implementation adds a Tour Reward system based on team business distribution:

**Requirements:**
- **Total Team Business**: $10,000
- **Business Distribution**: 50% from STRONGEST direct user + 50% from ALL OTHER direct users
- **Reward**: Europe Tour eligibility

**Logic:**
1. Find all direct users of the person
2. Sort them by team business (highest to lowest)
3. Take 50% from the BIGGEST direct user's team business
4. Take 50% from the SUM of ALL OTHER direct users' team business
5. Add both together and check if ≥ $10,000

## How It Works

### 1. Daily Calculation (`cron_tb.php`)
The daily team business cron calculates:
- **All Direct Users**: Gets all direct referrals and their team business
- **Strongest Direct**: The direct user with highest team business
- **Other Directs**: Sum of all other direct users' team business
- **Tour Business**: `(Strongest Direct × 0.5) + (Other Directs × 0.5)`

### 2. Eligibility Check
```php
if($tour_business >= 10000) {
    // Mark user as eligible for Tour Reward
    my_query("UPDATE user SET tour_eligible = 1 WHERE uid = '$uid'");
} else {
    my_query("UPDATE user SET tour_eligible = 0 WHERE uid = '$uid'");
}
```

### 3. Weekly Reward Processing (`cron_weekly_rewards.php`)
When the weekly cron runs, it checks for Tour Reward eligibility and records it in the `income_reward` table.

## Database Changes Required

### User Table
**No new columns needed!** Uses your existing fields:
- `tbl` (field 61): Stores strongest direct user's team business
- `tbr` (field 62): Stores sum of other direct users' team business
- `got` (field 65): Tour eligibility flag (0=not eligible, 1=eligible)
- `teamb` (field 59): Total team business (already exists)

### Income Reward Table
The system uses your existing `income_reward` table with:
- `reward = 99` (Special code for Tour Reward)
- `type = 2` (Special rewards type)
- `status = 1` (Active/Completed)

## Files Modified

### 1. `cron_tb.php` (Modified)
**Added:**
- Tour business calculation logic
- Strong leg vs weak leg determination
- 50/50 business distribution calculation
- Tour eligibility marking

**Key Changes:**
```php
// Tour Reward Logic: 50% from strongest direct + 50% from all others
// Step 1: Get all direct users sorted by team business
$direct_users_query = "SELECT uid, teamb FROM user WHERE refer_id = '".$uid."' AND status = 0 AND topup > 0 ORDER BY teamb DESC";
$direct_users_result = my_query($direct_users_query);

// Step 2: Calculate 50% from strongest + 50% from others
if(count($direct_users) > 0) {
    $strongest_direct_business = $direct_users[0]['teamb'];
    $strongest_50_percent = $strongest_direct_business * 0.5;

    $other_directs_sum = 0;
    for($i = 1; $i < count($direct_users); $i++) {
        $other_directs_sum += $direct_users[$i]['teamb'];
    }
    $others_50_percent = $other_directs_sum * 0.5;

    $tour_business = $strongest_50_percent + $others_50_percent;
}
```

### 2. `cron_weekly_rewards.php` (Modified)
**Added:**
- Tour Reward eligibility check
- Duplicate prevention (one Tour Reward per user)
- Recording in `income_reward` table
- Detailed logging

**Key Features:**
- Checks `tour_eligible = 1` flag
- Prevents duplicate Tour Rewards
- Records eligibility even if no monetary reward
- Integrates with existing 3x limit system

## Example Scenarios

### Scenario 1: Qualified User
**Direct Users:**
- Direct User A: $15,000 (Strongest)
- Direct User B: $8,000 (Other)
- Direct User C: $5,000 (Other)
- Direct User D: $2,000 (Other)

**Calculation:**
- 50% from Strongest: $15,000 × 0.5 = $7,500
- 50% from Others: ($8,000 + $5,000 + $2,000) × 0.5 = $7,500
- **Tour Business**: $7,500 + $7,500 = $15,000
- **Result**: ✅ Eligible for Tour Reward

### Scenario 2: Not Qualified User
**Direct Users:**
- Direct User A: $6,000 (Strongest)
- Direct User B: $3,000 (Other)
- Direct User C: $1,000 (Other)

**Calculation:**
- 50% from Strongest: $6,000 × 0.5 = $3,000
- 50% from Others: ($3,000 + $1,000) × 0.5 = $2,000
- **Tour Business**: $3,000 + $2,000 = $5,000
- **Result**: ❌ Not eligible (less than $10,000)

### Scenario 3: Only One Strong Direct
**Direct Users:**
- Direct User A: $18,000 (Only one direct)

**Calculation:**
- 50% from Strongest: $18,000 × 0.5 = $9,000
- 50% from Others: $0 × 0.5 = $0
- **Tour Business**: $9,000 + $0 = $9,000
- **Result**: ❌ Not eligible (need other directs too)

## Cron Schedule

### Daily Team Business Calculation
```bash
# Run cron_tb.php daily at 2:00 AM
0 2 * * * /usr/bin/php /path/to/cron_tb.php
```

### Weekly Reward Processing
```bash
# Run cron_weekly_rewards.php every Sunday at 11:59 PM
59 23 * * 0 /usr/bin/php /path/to/cron_weekly_rewards.php
```

## Output Examples

### Daily Cron Output (`cron_tb.php`)
```
User UID: 123 - Team Business: $15,000 - Tour Business: $8,000 - Not Eligible
User UID: 456 - Team Business: $20,000 - Tour Business: $12,000 - Eligible for Tour Reward
```

### Weekly Cron Output (`cron_weekly_rewards.php`)
```
✅ Tour Reward (Europe Tour) eligibility recorded for user USER123 (UID: 456) - Team Business: $12,000.00
ℹ User USER456 (UID: 789) already received Tour Reward
```

## Database Queries

### Check Tour Eligible Users
```sql
SELECT uid, login_id, tour_business, tour_eligible 
FROM user 
WHERE tour_eligible = 1;
```

### Check Tour Rewards Given
```sql
SELECT ir.*, u.login_id, u.tour_business
FROM income_reward ir
JOIN user u ON ir.uid = u.uid
WHERE ir.reward = 99 AND ir.type = 2
ORDER BY ir.datetime DESC;
```

### Tour Reward Statistics
```sql
SELECT 
    COUNT(*) as total_eligible,
    COUNT(CASE WHEN ir.uid IS NOT NULL THEN 1 END) as rewards_given
FROM user u
LEFT JOIN income_reward ir ON u.uid = ir.uid AND ir.reward = 99 AND ir.type = 2
WHERE u.tour_eligible = 1;
```

## Customization

### Change Tour Business Requirement
Edit the threshold in `cron_tb.php`:
```php
if($tour_business >= 15000) { // Change from 10000 to 15000
    my_query("UPDATE user SET tour_eligible = 1 WHERE uid = '$uid'");
}
```

### Add Monetary Reward
Edit the reward amount in `cron_weekly_rewards.php`:
```php
$tour_reward_amount = 1000; // Set actual reward amount
```

### Change Business Distribution Logic
Modify the calculation in `cron_tb.php`:
```php
// Example: Require exactly 50/50 split
$min_leg_required = $total_team_business * 0.5;
if($weak_leg >= $min_leg_required && $total_team_business >= 10000) {
    // Eligible
}
```

## Testing

### Manual Testing
1. **Run daily cron**: `php cron_tb.php`
2. **Check eligibility**: Query `user` table for `tour_eligible = 1`
3. **Run weekly cron**: `php cron_weekly_rewards.php`
4. **Verify records**: Check `income_reward` table for `reward = 99`

### Test Data Setup
```sql
-- Create test user with qualifying business
UPDATE user SET teamb = 20000 WHERE uid = 123;

-- Run the cron manually to see results
-- Check if tour_eligible is set to 1
SELECT uid, teamb, tour_business, tour_eligible FROM user WHERE uid = 123;
```

## Integration Notes

- **Existing System**: Integrates with current team business calculation
- **3x Limit**: Respects existing reward limit system
- **Duplicate Prevention**: Prevents multiple Tour Rewards per user
- **Logging**: Provides detailed output for monitoring
- **Database**: Uses existing table structure with minimal additions

## Support

The system integrates seamlessly with your existing:
- Team business calculation (`cron_tb.php`)
- Weekly reward processing (`cron_weekly_rewards.php`)
- Database helper functions (`my_query`, `my_num_rows`)
- Reward limit system (`check_3x`)
- Income tracking (`income_reward` table)

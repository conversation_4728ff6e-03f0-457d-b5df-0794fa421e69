<!DOCTYPE html>
<html>
<head>
    <title>Test Form Submission</title>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
</head>
<body>
    <h1>Test Form Submission</h1>
    
    <h2>Simple Test Form</h2>
    <form action="update_wallet_model.php" method="post">
        <input type="hidden" name="uid" value="1" />
        <input type="hidden" name="otp_verified" value="true" />
        <label>Bitcoin Address:</label>
        <input type="text" name="bitcoin" value="******************************************" />
        <br><br>
        <button type="submit">Test Submit</button>
    </form>
    
    <h2>Debug Steps:</h2>
    <ol>
        <li>Click the "Test Submit" button above</li>
        <li>Check if update_wallet_model.php is called</li>
        <li>Check browser network tab for the request</li>
        <li>Check server logs for the debug messages</li>
    </ol>
    
    <h2>Manual Test in Profile:</h2>
    <ol>
        <li>Go to profile.php</li>
        <li>Open browser console</li>
        <li>Run: <code>$('.wallet-form')[0].submit()</code></li>
        <li>This will force submit the form bypassing all JavaScript</li>
    </ol>
    
    <script>
    $('form').on('submit', function(e) {
        console.log('Form submitting to:', $(this).attr('action'));
        console.log('Form method:', $(this).attr('method'));
        
        // Log form data
        const formData = new FormData(this);
        console.log('Form data:');
        for (let [key, value] of formData.entries()) {
            console.log(key + ':', value);
        }
    });
    </script>
</body>
</html>
